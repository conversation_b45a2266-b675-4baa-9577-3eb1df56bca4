# 🕌 Halal Restaurant Directory

A modern web application for discovering and sharing halal restaurants, built with Next.js 15 and integrated with Google Places API.

## 🌟 Features

### 🔍 Restaurant Discovery

- **Hybrid Search**: Combines local database with Google Places API for comprehensive results
- **Location-Based Search**: Find restaurants near you with radius-based filtering
- **Advanced Filtering**: Filter by cuisine type, price level, verification status
- **Real-Time Results**: Fast search with optimized database queries

### 🏪 Restaurant Management

- **Google Places Integration**: Add restaurants directly from Google Places search
- **User Contributions**: Community-driven restaurant additions and updates
- **Verification System**: Manual verification for quality assurance
- **Rich Details**: Comprehensive restaurant information including hours, contact, photos

### 📱 User Experience

- **Responsive Design**: Mobile-first design that works on all devices
- **Modern UI**: Clean, intuitive interface built with shadcn/ui components
- **Fast Performance**: Optimized with Next.js 15 App Router and server components
- **SEO Optimized**: Dynamic metadata and structured data for search engines

### 🔐 Data & Security

- **Google API Compliance**: Follows Google Places API Terms of Service
- **Data Privacy**: Secure handling of user data and API keys
- **Performance Caching**: Intelligent caching system for optimal performance
- **Error Handling**: Graceful error handling and user feedback

## 🛠️ Technology Stack

### Frontend

- **Next.js 15** - React framework with App Router
- **React 19** - Latest React features and optimizations
- **Tailwind CSS 4** - Utility-first CSS framework
- **shadcn/ui** - Modern component library
- **Lucide React** - Beautiful icon library

### Backend

- **Next.js API Routes** - Server-side API endpoints
- **Prisma ORM** - Type-safe database operations
- **PostgreSQL** - Robust relational database
- **Server Actions** - Secure form handling

### External APIs

- **Google Places API** - Restaurant discovery and details
- **Google Maps API** - Photo proxy and directions

## 🚀 Getting Started

### Prerequisites

- Node.js 18+
- PostgreSQL database
- Google Places API key

### Installation

1. **Clone the repository**

   ```bash
   git clone <repository-url>
   cd directory-google
   ```

2. **Install dependencies**

   ```bash
   npm install
   ```

3. **Set up environment variables**

   ```bash
   cp .env.example .env.local
   ```

   Configure the following variables:

   ```env
   DATABASE_URL="postgresql://username:password@localhost:5432/halal_directory"
   GOOGLE_PLACES_API_KEY="your_google_places_api_key"
   NEXT_PUBLIC_GOOGLE_MAPS_API_KEY="your_google_maps_api_key"
   ```

4. **Set up the database**

   ```bash
   npx prisma generate
   npx prisma db push
   ```

5. **Seed the database (optional)**

   ```bash
   node scripts/seed.js
   ```

6. **Start the development server**

   ```bash
   npm run dev
   ```

7. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 📁 Project Structure

```
directory-google/
├── app/                          # Next.js App Router
│   ├── actions/                  # Server Actions
│   ├── api/                      # API Routes
│   ├── components/               # React Components
│   ├── lib/                      # Utility Libraries
│   ├── restaurant/               # Restaurant Detail Routes
│   ├── add-restaurant/           # Restaurant Addition Page
│   └── globals.css              # Global Styles
├── components/ui/                # Shared UI Components
├── prisma/                      # Database Schema
├── scripts/                     # Utility Scripts
└── ARCHITECTURE.md              # Detailed Architecture Documentation
```

## 🗄️ Database Schema

The application uses a PostgreSQL database with the following main entities:

- **Restaurant** - Core restaurant information and metadata
- **User** - User accounts and preferences
- **Review** - User reviews and ratings
- **RestaurantPhoto** - User-uploaded restaurant photos
- **GooglePlacesCache** - Temporary cache for Google Places data

See [ARCHITECTURE.md](./ARCHITECTURE.md) for detailed schema documentation.

## 🔧 Configuration

### Google Places API Setup

1. **Create a Google Cloud Project**

   - Go to [Google Cloud Console](https://console.cloud.google.com/)
   - Create a new project or select existing one

2. **Enable APIs**

   - Enable Places API
   - Enable Maps JavaScript API (for photos)

3. **Create API Keys**

   - Create API key for server-side usage (Places API)
   - Create API key for client-side usage (Maps JavaScript API)
   - Restrict keys appropriately for security

4. **Set Usage Limits**
   - Set daily quotas to control costs
   - Monitor usage in Google Cloud Console

### Database Configuration

The application supports PostgreSQL databases. You can use:

- Local PostgreSQL installation
- Docker PostgreSQL container
- Managed services (Vercel Postgres, Supabase, etc.)

## 🚀 Deployment

### Vercel (Recommended)

1. **Connect Repository**

   - Import project to Vercel
   - Connect your Git repository

2. **Configure Environment Variables**

   - Add all environment variables in Vercel dashboard
   - Ensure API keys are properly configured

3. **Database Setup**

   - Use Vercel Postgres or external PostgreSQL service
   - Run migrations: `npx prisma db push`

4. **Deploy**
   - Automatic deployment on Git push
   - Monitor deployment logs for issues

### Self-Hosting

1. **Build the Application**

   ```bash
   npm run build
   ```

2. **Start Production Server**

   ```bash
   npm start
   ```

3. **Set up Reverse Proxy**
   - Configure Nginx or Apache
   - Set up SSL certificates
   - Configure domain and DNS

## 📊 Performance

### Optimization Features

- **Server Components** - Default server-side rendering
- **Image Optimization** - Next.js Image component with lazy loading
- **Database Indexing** - Strategic indexes for common queries
- **API Caching** - Google Places data caching (30-day compliance)
- **Code Splitting** - Automatic route-based code splitting

### Monitoring

- Monitor Google Places API usage and costs
- Track database performance and query optimization
- Use Next.js built-in analytics for performance insights

## 🤝 Contributing

1. **Fork the repository**
2. **Create a feature branch** (`git checkout -b feature/amazing-feature`)
3. **Commit your changes** (`git commit -m 'Add amazing feature'`)
4. **Push to the branch** (`git push origin feature/amazing-feature`)
5. **Open a Pull Request**

### Development Guidelines

- Follow existing code style and patterns
- Add comments for complex logic
- Test changes thoroughly
- Update documentation as needed

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [Next.js](https://nextjs.org/) - The React framework for production
- [Prisma](https://www.prisma.io/) - Next-generation ORM
- [shadcn/ui](https://ui.shadcn.com/) - Beautiful component library
- [Google Places API](https://developers.google.com/maps/documentation/places/web-service) - Restaurant data
- [Lucide](https://lucide.dev/) - Beautiful icons

## 📞 Support

For support and questions:

- Check the [ARCHITECTURE.md](./ARCHITECTURE.md) for detailed documentation
- Review existing issues in the repository
- Create a new issue for bugs or feature requests

---

**Built with ❤️ for the Muslim community**
