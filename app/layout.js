import { Geist, <PERSON>eist_Mono } from "next/font/google";
import "./globals.css";
import Navigation from "./components/Navigation";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata = {
  title: "Halal Restaurant Directory | Find Authentic Halal Food",
  description:
    "Discover and explore authentic halal restaurants in your area. Search by cuisine, location, and add new restaurants to help the community.",
  keywords:
    "halal restaurants, halal food, muslim dining, restaurant directory",
};

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <Navigation />
        {children}
      </body>
    </html>
  );
}
