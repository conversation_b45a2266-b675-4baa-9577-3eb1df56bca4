/**
 * RESTAURANT SERVER ACTIONS
 *
 * This module contains Next.js Server Actions for restaurant-related operations.
 * Server Actions provide a secure way to handle form submissions and data mutations
 * directly from React components without creating separate API routes.
 *
 * Key Features:
 * - Hybrid search (local database + Google Places API)
 * - Restaurant creation and management
 * - Google Places integration for restaurant discovery
 * - Form validation and error handling
 * - Cache revalidation for updated data
 * - Secure server-side processing
 *
 * Architecture:
 * - Server Actions for form handling and mutations
 * - Integration with database service layer
 * - Google Places API integration
 * - Consistent error handling and validation
 * - Automatic cache invalidation
 *
 * Security:
 * - Server-side validation for all inputs
 * - Sanitization of user-provided data
 * - Rate limiting through API integration
 * - Secure handling of sensitive operations
 *
 * @see https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations
 */

"use server";

import { redirect } from "next/navigation";
import { revalidatePath } from "next/cache";
import { restaurantService } from "../lib/db.js";
import {
  searchHalalRestaurants,
  searchPlacesByText,
  getPlaceDetails,
} from "../lib/googlePlaces.js";

/**
 * Search restaurants using Google Places API and local database
 */
export async function searchRestaurants(query, location, filters = {}) {
  try {
    let results = [];

    // If we have a location, search Google Places API
    if (location || query) {
      try {
        let googleResults = [];

        if (location && query) {
          // Search by text with location bias
          googleResults = await searchPlacesByText(query, location);
        } else if (location) {
          // Search by location for halal restaurants
          googleResults = await searchHalalRestaurants(location);
        } else if (query) {
          // Search by text only
          googleResults = await searchPlacesByText(query);
        }

        // Transform Google results to match our format
        results = googleResults.map((place) => ({
          id: place.placeId,
          name: place.name,
          address: place.address,
          rating: place.rating,
          priceLevel: place.priceLevel,
          isOpen: place.isOpen,
          photos: place.photos,
          geometry: place.geometry,
          source: "google",
          googlePlaceId: place.placeId,
        }));
      } catch (error) {
        console.error("Error searching Google Places:", error);
      }
    }

    // Also search local database
    try {
      const localResults = await restaurantService.search(query || "", {
        city: location,
        ...filters,
      });

      // Transform local results
      const localFormatted = localResults.map((restaurant) => ({
        id: restaurant.id,
        name: restaurant.name,
        address: restaurant.address,
        rating: restaurant.googleRating || 0,
        priceLevel: restaurant.priceLevel,
        isOpen: null,
        photos: restaurant.photos,
        geometry: {
          lat: restaurant.latitude,
          lng: restaurant.longitude,
        },
        source: "local",
        googlePlaceId: restaurant.googlePlaceId,
        isVerified: restaurant.isVerified,
        reviewCount: restaurant._count?.reviews || 0,
      }));

      // Merge results, avoiding duplicates based on Google Place ID
      const googlePlaceIds = new Set(
        results.map((r) => r.googlePlaceId).filter(Boolean)
      );
      const uniqueLocalResults = localFormatted.filter(
        (r) => !r.googlePlaceId || !googlePlaceIds.has(r.googlePlaceId)
      );

      results = [...results, ...uniqueLocalResults];
    } catch (error) {
      console.error("Error searching local database:", error);
    }

    return {
      success: true,
      data: results,
      total: results.length,
    };
  } catch (error) {
    console.error("Error in searchRestaurants:", error);
    return {
      success: false,
      error: "Failed to search restaurants",
      data: [],
      total: 0,
    };
  }
}

/**
 * Get restaurant details by ID (local) or Place ID (Google)
 */
export async function getRestaurantDetails(id, isGooglePlace = false) {
  try {
    if (isGooglePlace) {
      // Get details from Google Places API
      const googleDetails = await getPlaceDetails(id);

      // Check if we have this restaurant in our local database
      const localRestaurant = await restaurantService.findByGooglePlaceId(id);

      return {
        success: true,
        data: {
          ...googleDetails,
          localData: localRestaurant,
          source: "google",
        },
      };
    } else {
      // Get from local database
      const restaurant = await restaurantService.findById(id);

      if (!restaurant) {
        return {
          success: false,
          error: "Restaurant not found",
        };
      }

      // If restaurant has Google Place ID, get fresh Google data
      let googleData = null;
      if (restaurant.googlePlaceId) {
        try {
          googleData = await getPlaceDetails(restaurant.googlePlaceId);
        } catch (error) {
          console.error("Error fetching Google data:", error);
        }
      }

      return {
        success: true,
        data: {
          ...restaurant,
          googleData,
          source: "local",
        },
      };
    }
  } catch (error) {
    console.error("Error getting restaurant details:", error);
    return {
      success: false,
      error: "Failed to get restaurant details",
    };
  }
}

/**
 * Add a new restaurant to the local database
 */
export async function addRestaurant(formData) {
  try {
    const data = {
      name: formData.get("name"),
      description: formData.get("description"),
      address: formData.get("address"),
      phone: formData.get("phone"),
      website: formData.get("website"),
      email: formData.get("email"),
      latitude: parseFloat(formData.get("latitude")),
      longitude: parseFloat(formData.get("longitude")),
      city: formData.get("city"),
      state: formData.get("state"),
      country: formData.get("country") || "US",
      zipCode: formData.get("zipCode"),
      cuisine:
        formData
          .get("cuisine")
          ?.split(",")
          .map((c) => c.trim()) || [],
      priceLevel: formData.get("priceLevel")
        ? parseInt(formData.get("priceLevel"))
        : null,
      halalCertification: formData.get("halalCertification"),
      googlePlaceId: formData.get("googlePlaceId") || null,
      // Note: addedById would come from user session in a real app
      addedById: null,
    };

    // Validate required fields
    if (
      !data.name ||
      !data.address ||
      !data.latitude ||
      !data.longitude ||
      !data.city
    ) {
      return {
        success: false,
        error:
          "Missing required fields: name, address, location, and city are required",
      };
    }

    const restaurant = await restaurantService.create(data);

    revalidatePath("/");
    revalidatePath("/restaurants");

    return {
      success: true,
      data: restaurant,
      message: "Restaurant added successfully!",
    };
  } catch (error) {
    console.error("Error adding restaurant:", error);
    return {
      success: false,
      error: "Failed to add restaurant. Please try again.",
    };
  }
}

/**
 * Search Google Places API directly (for add restaurant page)
 */
export async function searchGooglePlaces(query, location = "") {
  try {
    let results = [];

    if (location && query) {
      // Search by text with location bias
      results = await searchPlacesByText(query, location);
    } else if (query) {
      // Search by text only
      results = await searchPlacesByText(query);
    } else {
      return {
        success: false,
        error: "Please provide a search query",
        data: [],
      };
    }

    return {
      success: true,
      data: results,
      total: results.length,
    };
  } catch (error) {
    console.error("Error in searchGooglePlaces:", error);
    return {
      success: false,
      error: "Failed to search Google Places",
      data: [],
      total: 0,
    };
  }
}

/**
 * Add restaurant from Google Places data
 */
export async function addRestaurantFromGoogle(googlePlaceData) {
  try {
    const { placeId, name, address, rating, priceLevel, geometry, types } =
      googlePlaceData;

    // Check if restaurant already exists
    const existingRestaurant = await restaurantService.findByGooglePlaceId(
      placeId
    );
    if (existingRestaurant) {
      return {
        success: false,
        error: "This restaurant is already in our directory",
      };
    }

    // Get detailed information from Google Places
    const detailedData = await getPlaceDetails(placeId);

    // Extract city from address (simple approach)
    const addressParts = address.split(",");
    const city =
      addressParts.length > 1
        ? addressParts[addressParts.length - 2].trim()
        : "Unknown";
    const state =
      addressParts.length > 2
        ? addressParts[addressParts.length - 1].trim()
        : "";

    // Determine cuisine types from Google Places types
    const cuisineTypes = types
      ?.filter(
        (type) =>
          type.includes("restaurant") ||
          type.includes("food") ||
          type.includes("meal") ||
          [
            "indian_restaurant",
            "middle_eastern_restaurant",
            "turkish_restaurant",
          ].includes(type)
      )
      .map((type) => type.replace(/_/g, " ").replace("restaurant", "").trim())
      .filter(Boolean) || ["Restaurant"];

    const data = {
      name,
      description: `Halal restaurant found via Google Places`,
      address,
      phone: detailedData.phone || null,
      website: detailedData.website || null,
      latitude: geometry.lat,
      longitude: geometry.lng,
      city,
      state,
      country: "US", // Default, could be improved with geocoding
      cuisine: cuisineTypes,
      priceLevel: priceLevel || null,
      googlePlaceId: placeId,
      isHalal: true,
      isVerified: false, // Will need manual verification
      // Note: addedById would come from user session in a real app
      addedById: null,
    };

    const restaurant = await restaurantService.create(data);

    revalidatePath("/");
    revalidatePath("/restaurants");

    return {
      success: true,
      data: restaurant,
      message:
        "Restaurant added successfully! It will appear in search results.",
    };
  } catch (error) {
    console.error("Error adding restaurant from Google:", error);
    return {
      success: false,
      error: "Failed to add restaurant. Please try again.",
    };
  }
}

/**
 * Search action for form submission
 */
export async function searchAction(formData) {
  const query = formData.get("query")?.toString() || "";
  const location = formData.get("location")?.toString() || "";

  const params = new URLSearchParams();
  if (query) params.set("q", query);
  if (location) params.set("location", location);

  redirect(`/?${params.toString()}`);
}
