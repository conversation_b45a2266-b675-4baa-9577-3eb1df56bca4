/**
 * DATABASE SERVICE LAYER
 *
 * This module provides a service layer abstraction over Prisma ORM for database operations.
 * It encapsulates all database queries and provides a clean API for the application layer.
 *
 * Key Features:
 * - Restaurant CRUD operations with complex filtering
 * - Location-based search with radius calculations
 * - Review management with moderation support
 * - User management and authentication support
 * - Google Places cache management
 * - Optimized queries with proper includes and indexing
 *
 * Architecture:
 * - Service pattern for separation of concerns
 * - Singleton pattern for Prisma client in development
 * - Consistent error handling and validation
 * - Performance-optimized queries with selective includes
 * - Transaction support for data consistency
 *
 * @see https://www.prisma.io/docs/concepts/components/prisma-client
 */

import { PrismaClient } from "../generated/prisma/index.js";

// Global variable to store the Prisma client instance
let prisma;

/**
 * PRISMA CLIENT INITIALIZATION
 *
 * Uses singleton pattern in development to prevent multiple client instances
 * during Next.js hot reloads, which can cause connection pool exhaustion.
 * In production, creates a single client instance.
 */
if (process.env.NODE_ENV === "production") {
  // Production: Single client instance
  prisma = new PrismaClient({
    log: ["error"], // Only log errors in production
  });
} else {
  // Development: Singleton pattern to prevent hot reload issues
  if (!global.prisma) {
    global.prisma = new PrismaClient({
      log: ["query", "info", "warn", "error"], // Verbose logging in development
    });
  }
  prisma = global.prisma;
}

// Export the Prisma client for direct use when needed
export default prisma;

/**
 * RESTAURANT DATABASE OPERATIONS
 *
 * Comprehensive service for managing restaurant data including:
 * - CRUD operations with validation
 * - Location-based search with geographic calculations
 * - Text search with fuzzy matching
 * - Google Places integration support
 * - Performance-optimized queries with proper includes
 */
export const restaurantService = {
  /**
   * CREATE RESTAURANT
   *
   * Creates a new restaurant record with full relationship data.
   * Includes photos, reviews with author details, and added-by user information.
   *
   * @param {Object} data - Restaurant data object
   * @param {string} data.name - Restaurant name (required)
   * @param {string} data.address - Full address (required)
   * @param {number} data.latitude - GPS latitude (required)
   * @param {number} data.longitude - GPS longitude (required)
   * @param {string} data.city - City name (required)
   * @param {string[]} [data.cuisine] - Array of cuisine types
   * @param {number} [data.priceLevel] - Price level 1-4
   * @param {string} [data.googlePlaceId] - Google Place ID for correlation
   * @returns {Promise<Object>} Created restaurant with relationships
   */
  async create(data) {
    return await prisma.restaurant.create({
      data,
      include: {
        photos: true, // Include all restaurant photos
        reviews: {
          include: {
            author: {
              select: {
                id: true,
                name: true,
                avatar: true, // User profile info for review display
              },
            },
          },
        },
        addedBy: {
          select: {
            id: true,
            name: true, // User who added this restaurant
          },
        },
      },
    });
  },

  // Find restaurants by location with radius
  async findByLocation(lat, lng, radiusKm = 10, filters = {}) {
    const { cuisine, priceLevel, isVerified, limit = 20, offset = 0 } = filters;

    // Calculate approximate lat/lng bounds for the radius
    const latDelta = radiusKm / 111; // Rough conversion: 1 degree lat ≈ 111 km
    const lngDelta = radiusKm / (111 * Math.cos((lat * Math.PI) / 180));

    const where = {
      latitude: {
        gte: lat - latDelta,
        lte: lat + latDelta,
      },
      longitude: {
        gte: lng - lngDelta,
        lte: lng + lngDelta,
      },
      isHalal: true,
    };

    if (cuisine && cuisine.length > 0) {
      where.cuisine = {
        hasSome: cuisine,
      };
    }

    if (priceLevel) {
      where.priceLevel = priceLevel;
    }

    if (isVerified !== undefined) {
      where.isVerified = isVerified;
    }

    return await prisma.restaurant.findMany({
      where,
      include: {
        photos: {
          where: { isPrimary: true },
          take: 1,
        },
        reviews: {
          take: 3,
          orderBy: { createdAt: "desc" },
          include: {
            author: {
              select: {
                id: true,
                name: true,
                avatar: true,
              },
            },
          },
        },
        _count: {
          select: {
            reviews: true,
          },
        },
      },
      take: limit,
      skip: offset,
      orderBy: [
        { isVerified: "desc" },
        { googleRating: "desc" },
        { createdAt: "desc" },
      ],
    });
  },

  // Search restaurants by text
  async search(query, filters = {}) {
    const { city, cuisine, priceLevel, limit = 20, offset = 0 } = filters;

    const where = {
      isHalal: true,
      OR: [
        { name: { contains: query, mode: "insensitive" } },
        { description: { contains: query, mode: "insensitive" } },
        { address: { contains: query, mode: "insensitive" } },
        { cuisine: { hasSome: [query] } },
      ],
    };

    if (city) {
      where.city = { contains: city, mode: "insensitive" };
    }

    if (cuisine && cuisine.length > 0) {
      where.cuisine = {
        hasSome: cuisine,
      };
    }

    if (priceLevel) {
      where.priceLevel = priceLevel;
    }

    return await prisma.restaurant.findMany({
      where,
      include: {
        photos: {
          where: { isPrimary: true },
          take: 1,
        },
        reviews: {
          take: 3,
          orderBy: { createdAt: "desc" },
          include: {
            author: {
              select: {
                id: true,
                name: true,
                avatar: true,
              },
            },
          },
        },
        _count: {
          select: {
            reviews: true,
          },
        },
      },
      take: limit,
      skip: offset,
      orderBy: [
        { isVerified: "desc" },
        { googleRating: "desc" },
        { createdAt: "desc" },
      ],
    });
  },

  // Get restaurant by ID with full details
  async findById(id) {
    return await prisma.restaurant.findUnique({
      where: { id },
      include: {
        photos: true,
        reviews: {
          orderBy: { createdAt: "desc" },
          include: {
            author: {
              select: {
                id: true,
                name: true,
                avatar: true,
              },
            },
          },
        },
        addedBy: {
          select: {
            id: true,
            name: true,
          },
        },
        _count: {
          select: {
            reviews: true,
          },
        },
      },
    });
  },

  // Update restaurant
  async update(id, data) {
    return await prisma.restaurant.update({
      where: { id },
      data,
      include: {
        photos: true,
        reviews: {
          include: {
            author: {
              select: {
                id: true,
                name: true,
                avatar: true,
              },
            },
          },
        },
      },
    });
  },

  // Delete restaurant
  async delete(id) {
    return await prisma.restaurant.delete({
      where: { id },
    });
  },

  // Find by Google Place ID
  async findByGooglePlaceId(googlePlaceId) {
    return await prisma.restaurant.findUnique({
      where: { googlePlaceId },
      include: {
        photos: true,
        reviews: {
          include: {
            author: {
              select: {
                id: true,
                name: true,
                avatar: true,
              },
            },
          },
        },
      },
    });
  },
};

/**
 * Review database operations
 */
export const reviewService = {
  // Create a new review
  async create(data) {
    return await prisma.review.create({
      data,
      include: {
        author: {
          select: {
            id: true,
            name: true,
            avatar: true,
          },
        },
        restaurant: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });
  },

  // Get reviews for a restaurant
  async findByRestaurant(restaurantId, limit = 10, offset = 0) {
    return await prisma.review.findMany({
      where: {
        restaurantId,
        isApproved: true,
      },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            avatar: true,
          },
        },
      },
      orderBy: { createdAt: "desc" },
      take: limit,
      skip: offset,
    });
  },

  // Update review
  async update(id, data) {
    return await prisma.review.update({
      where: { id },
      data,
      include: {
        author: {
          select: {
            id: true,
            name: true,
            avatar: true,
          },
        },
      },
    });
  },

  // Delete review
  async delete(id) {
    return await prisma.review.delete({
      where: { id },
    });
  },
};

/**
 * User database operations
 */
export const userService = {
  // Create a new user
  async create(data) {
    return await prisma.user.create({
      data,
    });
  },

  // Find user by email
  async findByEmail(email) {
    return await prisma.user.findUnique({
      where: { email },
      include: {
        reviews: {
          include: {
            restaurant: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
        addedRestaurants: {
          select: {
            id: true,
            name: true,
            createdAt: true,
          },
        },
      },
    });
  },

  // Find user by ID
  async findById(id) {
    return await prisma.user.findUnique({
      where: { id },
      include: {
        reviews: {
          include: {
            restaurant: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
        addedRestaurants: {
          select: {
            id: true,
            name: true,
            createdAt: true,
          },
        },
      },
    });
  },

  // Update user
  async update(id, data) {
    return await prisma.user.update({
      where: { id },
      data,
    });
  },
};
