import { PrismaClient } from '../generated/prisma/index.js';

// Global variable to store the Prisma client instance
let prisma;

// Initialize Prisma client with singleton pattern for development
if (process.env.NODE_ENV === 'production') {
  prisma = new PrismaClient();
} else {
  // In development, use a global variable to preserve the instance across hot reloads
  if (!global.prisma) {
    global.prisma = new PrismaClient();
  }
  prisma = global.prisma;
}

export default prisma;

/**
 * Restaurant database operations
 */
export const restaurantService = {
  // Create a new restaurant
  async create(data) {
    return await prisma.restaurant.create({
      data,
      include: {
        photos: true,
        reviews: {
          include: {
            author: {
              select: {
                id: true,
                name: true,
                avatar: true,
              },
            },
          },
        },
        addedBy: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });
  },

  // Find restaurants by location with radius
  async findByLocation(lat, lng, radiusKm = 10, filters = {}) {
    const { cuisine, priceLevel, isVerified, limit = 20, offset = 0 } = filters;
    
    // Calculate approximate lat/lng bounds for the radius
    const latDelta = radiusKm / 111; // Rough conversion: 1 degree lat ≈ 111 km
    const lngDelta = radiusKm / (111 * Math.cos(lat * Math.PI / 180));

    const where = {
      latitude: {
        gte: lat - latDelta,
        lte: lat + latDelta,
      },
      longitude: {
        gte: lng - lngDelta,
        lte: lng + lngDelta,
      },
      isHalal: true,
    };

    if (cuisine && cuisine.length > 0) {
      where.cuisine = {
        hasSome: cuisine,
      };
    }

    if (priceLevel) {
      where.priceLevel = priceLevel;
    }

    if (isVerified !== undefined) {
      where.isVerified = isVerified;
    }

    return await prisma.restaurant.findMany({
      where,
      include: {
        photos: {
          where: { isPrimary: true },
          take: 1,
        },
        reviews: {
          take: 3,
          orderBy: { createdAt: 'desc' },
          include: {
            author: {
              select: {
                id: true,
                name: true,
                avatar: true,
              },
            },
          },
        },
        _count: {
          select: {
            reviews: true,
          },
        },
      },
      take: limit,
      skip: offset,
      orderBy: [
        { isVerified: 'desc' },
        { googleRating: 'desc' },
        { createdAt: 'desc' },
      ],
    });
  },

  // Search restaurants by text
  async search(query, filters = {}) {
    const { city, cuisine, priceLevel, limit = 20, offset = 0 } = filters;
    
    const where = {
      isHalal: true,
      OR: [
        { name: { contains: query, mode: 'insensitive' } },
        { description: { contains: query, mode: 'insensitive' } },
        { address: { contains: query, mode: 'insensitive' } },
        { cuisine: { hasSome: [query] } },
      ],
    };

    if (city) {
      where.city = { contains: city, mode: 'insensitive' };
    }

    if (cuisine && cuisine.length > 0) {
      where.cuisine = {
        hasSome: cuisine,
      };
    }

    if (priceLevel) {
      where.priceLevel = priceLevel;
    }

    return await prisma.restaurant.findMany({
      where,
      include: {
        photos: {
          where: { isPrimary: true },
          take: 1,
        },
        reviews: {
          take: 3,
          orderBy: { createdAt: 'desc' },
          include: {
            author: {
              select: {
                id: true,
                name: true,
                avatar: true,
              },
            },
          },
        },
        _count: {
          select: {
            reviews: true,
          },
        },
      },
      take: limit,
      skip: offset,
      orderBy: [
        { isVerified: 'desc' },
        { googleRating: 'desc' },
        { createdAt: 'desc' },
      ],
    });
  },

  // Get restaurant by ID with full details
  async findById(id) {
    return await prisma.restaurant.findUnique({
      where: { id },
      include: {
        photos: true,
        reviews: {
          orderBy: { createdAt: 'desc' },
          include: {
            author: {
              select: {
                id: true,
                name: true,
                avatar: true,
              },
            },
          },
        },
        addedBy: {
          select: {
            id: true,
            name: true,
          },
        },
        _count: {
          select: {
            reviews: true,
          },
        },
      },
    });
  },

  // Update restaurant
  async update(id, data) {
    return await prisma.restaurant.update({
      where: { id },
      data,
      include: {
        photos: true,
        reviews: {
          include: {
            author: {
              select: {
                id: true,
                name: true,
                avatar: true,
              },
            },
          },
        },
      },
    });
  },

  // Delete restaurant
  async delete(id) {
    return await prisma.restaurant.delete({
      where: { id },
    });
  },

  // Find by Google Place ID
  async findByGooglePlaceId(googlePlaceId) {
    return await prisma.restaurant.findUnique({
      where: { googlePlaceId },
      include: {
        photos: true,
        reviews: {
          include: {
            author: {
              select: {
                id: true,
                name: true,
                avatar: true,
              },
            },
          },
        },
      },
    });
  },
};

/**
 * Review database operations
 */
export const reviewService = {
  // Create a new review
  async create(data) {
    return await prisma.review.create({
      data,
      include: {
        author: {
          select: {
            id: true,
            name: true,
            avatar: true,
          },
        },
        restaurant: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });
  },

  // Get reviews for a restaurant
  async findByRestaurant(restaurantId, limit = 10, offset = 0) {
    return await prisma.review.findMany({
      where: {
        restaurantId,
        isApproved: true,
      },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            avatar: true,
          },
        },
      },
      orderBy: { createdAt: 'desc' },
      take: limit,
      skip: offset,
    });
  },

  // Update review
  async update(id, data) {
    return await prisma.review.update({
      where: { id },
      data,
      include: {
        author: {
          select: {
            id: true,
            name: true,
            avatar: true,
          },
        },
      },
    });
  },

  // Delete review
  async delete(id) {
    return await prisma.review.delete({
      where: { id },
    });
  },
};

/**
 * User database operations
 */
export const userService = {
  // Create a new user
  async create(data) {
    return await prisma.user.create({
      data,
    });
  },

  // Find user by email
  async findByEmail(email) {
    return await prisma.user.findUnique({
      where: { email },
      include: {
        reviews: {
          include: {
            restaurant: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
        addedRestaurants: {
          select: {
            id: true,
            name: true,
            createdAt: true,
          },
        },
      },
    });
  },

  // Find user by ID
  async findById(id) {
    return await prisma.user.findUnique({
      where: { id },
      include: {
        reviews: {
          include: {
            restaurant: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
        addedRestaurants: {
          select: {
            id: true,
            name: true,
            createdAt: true,
          },
        },
      },
    });
  },

  // Update user
  async update(id, data) {
    return await prisma.user.update({
      where: { id },
      data,
    });
  },
};
