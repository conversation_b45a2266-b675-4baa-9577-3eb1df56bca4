import { NextResponse } from 'next/server';

export async function GET(request, { params }) {
  const { photoReference } = params;
  const { searchParams } = new URL(request.url);
  const maxWidth = searchParams.get('maxwidth') || '400';

  if (!photoReference) {
    return new NextResponse('Photo reference is required', { status: 400 });
  }

  if (!process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY) {
    return new NextResponse('Google Maps API key not configured', { status: 500 });
  }

  try {
    // Construct Google Places Photo API URL
    const photoUrl = `https://maps.googleapis.com/maps/api/place/photo?maxwidth=${maxWidth}&photo_reference=${photoReference}&key=${process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY}`;

    // Fetch the image from Google
    const response = await fetch(photoUrl);

    if (!response.ok) {
      throw new Error(`Google API responded with status: ${response.status}`);
    }

    // Get the image data
    const imageBuffer = await response.arrayBuffer();
    const contentType = response.headers.get('content-type') || 'image/jpeg';

    // Return the image with appropriate headers
    return new NextResponse(imageBuffer, {
      status: 200,
      headers: {
        'Content-Type': contentType,
        'Cache-Control': 'public, max-age=86400', // Cache for 24 hours
      },
    });
  } catch (error) {
    console.error('Error fetching photo:', error);
    
    // Return a placeholder image or error response
    return new NextResponse('Failed to fetch photo', { status: 500 });
  }
}
