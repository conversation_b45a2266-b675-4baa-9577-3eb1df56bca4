import { NextResponse } from 'next/server';
import { Client } from '@googlemaps/google-maps-services-js';

export async function GET(request) {
  try {
    // Check if API key is configured
    if (!process.env.GOOGLE_PLACES_API_KEY) {
      return NextResponse.json({
        success: false,
        error: 'GOOGLE_PLACES_API_KEY is not configured in environment variables'
      }, { status: 500 });
    }

    const client = new Client({});
    
    // Test with a simple text search
    const response = await client.textSearch({
      params: {
        query: 'halal restaurant New York',
        key: process.env.GOOGLE_PLACES_API_KEY,
      },
    });

    return NextResponse.json({
      success: true,
      status: response.status,
      resultsCount: response.data.results?.length || 0,
      firstResult: response.data.results?.[0] ? {
        name: response.data.results[0].name,
        address: response.data.results[0].formatted_address,
        place_id: response.data.results[0].place_id,
      } : null,
      apiKeyConfigured: true,
    });

  } catch (error) {
    console.error('Google Places API Test Error:', error);
    
    let errorDetails = {
      success: false,
      error: error.message,
      apiKeyConfigured: !!process.env.GOOGLE_PLACES_API_KEY,
    };

    if (error.response) {
      errorDetails.httpStatus = error.response.status;
      errorDetails.apiError = error.response.data;
    }

    return NextResponse.json(errorDetails, { status: 500 });
  }
}
