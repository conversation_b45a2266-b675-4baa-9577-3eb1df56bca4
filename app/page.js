import { Suspense } from "react";
import SearchForm from "./components/SearchForm";
import RestaurantList from "./components/RestaurantList";

export default async function Home({ searchParams }) {
  const params = await searchParams;
  const query = params?.q || "";
  const location = params?.location || "";

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b bg-white/95 backdrop-blur supports-[backdrop-filter]:bg-white/60">
        <div className="container mx-auto px-4 py-6">
          <div className="flex flex-col items-center text-center space-y-4">
            <h1 className="text-4xl font-bold text-primary">
              🕌 Halal Restaurant Directory
            </h1>
            <p className="text-lg text-muted-foreground max-w-2xl">
              Discover authentic halal restaurants in your area. Search,
              explore, and add new halal dining options to help the community.
            </p>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8">
        {/* Search Section */}
        <div className="mb-8">
          <SearchForm initialQuery={query} initialLocation={location} />
        </div>

        {/* Results Section */}
        <Suspense fallback={<RestaurantListSkeleton />}>
          <RestaurantList query={query} location={location} />
        </Suspense>
      </main>

      {/* Footer */}
      <footer className="border-t bg-muted/50 mt-16">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center text-sm text-muted-foreground">
            <p>
              © 2024 Halal Restaurant Directory. Built with Next.js and Google
              Places API.
            </p>
            <p className="mt-2">
              Help us grow the community by adding your favorite halal
              restaurants!
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}

// Loading skeleton component
function RestaurantListSkeleton() {
  return (
    <div className="space-y-4">
      {[...Array(6)].map((_, i) => (
        <div key={i} className="border rounded-lg p-6 animate-pulse">
          <div className="flex space-x-4">
            <div className="w-24 h-24 bg-muted rounded-lg"></div>
            <div className="flex-1 space-y-2">
              <div className="h-6 bg-muted rounded w-3/4"></div>
              <div className="h-4 bg-muted rounded w-1/2"></div>
              <div className="h-4 bg-muted rounded w-2/3"></div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}
