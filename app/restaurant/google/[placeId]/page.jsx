import { notFound } from "next/navigation";
import { getRestaurantDetails } from "../../../actions/restaurant-actions";
import RestaurantDetailsPage from "../../../components/RestaurantDetailsPage";

export default async function GoogleRestaurantPage({ params }) {
  const { placeId } = await params;
  
  const result = await getRestaurantDetails(placeId, true);
  
  if (!result.success) {
    notFound();
  }

  return <RestaurantDetailsPage restaurant={result.data} />;
}

export async function generateMetadata({ params }) {
  const { placeId } = await params;
  
  const result = await getRestaurantDetails(placeId, true);
  
  if (!result.success) {
    return {
      title: "Restaurant Not Found",
    };
  }

  const restaurant = result.data;
  
  return {
    title: `${restaurant.name} - Halal Restaurant Directory`,
    description: restaurant.description || `Halal restaurant located at ${restaurant.address}`,
  };
}
