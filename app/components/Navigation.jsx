/**
 * NAVIGATION COMPONENT
 * 
 * Main navigation header for the application.
 * Provides consistent navigation across all pages.
 * 
 * Key Features:
 * - Logo and brand name
 * - Main navigation links
 * - Mobile responsive menu
 * - Active link highlighting
 * - Search shortcut
 * 
 * @see app/layout.js for usage
 */

'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Button } from '../../components/ui/button';
import { 
  Search, 
  Plus, 
  List, 
  Home,
  Menu,
  X
} from 'lucide-react';
import { useState } from 'react';

const navigationItems = [
  {
    name: 'Home',
    href: '/',
    icon: Home,
    description: 'Search restaurants'
  },
  {
    name: 'Browse All',
    href: '/restaurants',
    icon: List,
    description: 'View all restaurants'
  },
  {
    name: 'Add Restaurant',
    href: '/add-restaurant',
    icon: Plus,
    description: 'Add new restaurant'
  }
];

export default function Navigation() {
  const pathname = usePathname();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const isActive = (href) => {
    if (href === '/') {
      return pathname === '/';
    }
    return pathname.startsWith(href);
  };

  return (
    <nav className="border-b bg-white/95 backdrop-blur supports-[backdrop-filter]:bg-white/60 sticky top-0 z-50">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo and Brand */}
          <Link href="/" className="flex items-center space-x-3 hover:opacity-80 transition-opacity">
            <div className="text-2xl">🕌</div>
            <div>
              <div className="font-bold text-lg text-primary">
                Halal Directory
              </div>
              <div className="text-xs text-muted-foreground hidden sm:block">
                Find authentic halal restaurants
              </div>
            </div>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-1">
            {navigationItems.map((item) => {
              const Icon = item.icon;
              const active = isActive(item.href);
              
              return (
                <Link key={item.href} href={item.href}>
                  <Button
                    variant={active ? "default" : "ghost"}
                    size="sm"
                    className="flex items-center space-x-2"
                  >
                    <Icon className="h-4 w-4" />
                    <span>{item.name}</span>
                  </Button>
                </Link>
              );
            })}
          </div>

          {/* Mobile Menu Button */}
          <div className="md:hidden">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="p-2"
            >
              {isMobileMenuOpen ? (
                <X className="h-5 w-5" />
              ) : (
                <Menu className="h-5 w-5" />
              )}
            </Button>
          </div>
        </div>

        {/* Mobile Navigation Menu */}
        {isMobileMenuOpen && (
          <div className="md:hidden border-t bg-background/95 backdrop-blur">
            <div className="py-4 space-y-2">
              {navigationItems.map((item) => {
                const Icon = item.icon;
                const active = isActive(item.href);
                
                return (
                  <Link 
                    key={item.href} 
                    href={item.href}
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    <div className={`flex items-center space-x-3 px-4 py-3 rounded-lg transition-colors ${
                      active 
                        ? 'bg-primary text-primary-foreground' 
                        : 'hover:bg-muted'
                    }`}>
                      <Icon className="h-5 w-5" />
                      <div>
                        <div className="font-medium">{item.name}</div>
                        <div className="text-sm opacity-70">{item.description}</div>
                      </div>
                    </div>
                  </Link>
                );
              })}
            </div>
          </div>
        )}
      </div>
    </nav>
  );
}

/**
 * BREADCRUMB NAVIGATION COMPONENT
 * 
 * Shows the current page hierarchy for better navigation context.
 */
export function Breadcrumb({ items }) {
  return (
    <nav className="flex items-center space-x-2 text-sm text-muted-foreground mb-4">
      {items.map((item, index) => (
        <div key={index} className="flex items-center space-x-2">
          {index > 0 && <span>/</span>}
          {item.href ? (
            <Link 
              href={item.href} 
              className="hover:text-foreground transition-colors"
            >
              {item.name}
            </Link>
          ) : (
            <span className="text-foreground font-medium">{item.name}</span>
          )}
        </div>
      ))}
    </nav>
  );
}

/**
 * PAGE HEADER COMPONENT
 * 
 * Consistent page header with title, description, and optional actions.
 */
export function PageHeader({ title, description, children, breadcrumb }) {
  return (
    <div className="border-b bg-card">
      <div className="container mx-auto px-4 py-6">
        {breadcrumb && <Breadcrumb items={breadcrumb} />}
        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">{title}</h1>
            {description && (
              <p className="text-muted-foreground mt-2">{description}</p>
            )}
          </div>
          {children && (
            <div className="flex items-center gap-2">
              {children}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
