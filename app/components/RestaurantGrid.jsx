/**
 * RESTAURANT GRID COMPONENT
 *
 * This component displays restaurants in a responsive grid layout.
 * Each restaurant is shown as a card with key information and actions.
 *
 * Key Features:
 * - Responsive grid layout (1-3 columns based on screen size)
 * - Restaurant cards with images, ratings, and key info
 * - Verification badges and status indicators
 * - Price level indicators
 * - Quick action buttons
 * - Optimized images with fallbacks
 * - Hover effects and animations
 *
 * @see app/restaurants/page.jsx for usage
 */

import Link from "next/link";
import Image from "next/image";
import { Card, CardContent, CardFooter } from "../../components/ui/card";
import { Badge } from "../../components/ui/badge";
import { Button } from "../../components/ui/button";
import {
  Star,
  MapPin,
  Clock,
  Phone,
  CheckCircle,
  ExternalLink,
  Heart,
  Share2,
  DollarSign,
} from "lucide-react";

/**
 * RESTAURANT GRID COMPONENT
 */
export default function RestaurantGrid({ restaurants }) {
  if (!restaurants || restaurants.length === 0) {
    return null;
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
      {restaurants.map((restaurant) => (
        <RestaurantCard key={restaurant.id} restaurant={restaurant} />
      ))}
    </div>
  );
}

/**
 * INDIVIDUAL RESTAURANT CARD COMPONENT
 */
function RestaurantCard({ restaurant }) {
  const {
    id,
    name,
    description,
    address,
    city,
    cuisine,
    priceLevel,
    rating,
    verified,
    photos,
    isOpen,
    phone,
    website,
    googlePlaceId,
  } = restaurant;

  // Get primary photo
  const primaryPhoto = photos?.[0];
  const photoUrl = primaryPhoto
    ? `/api/photo/${primaryPhoto.photoReference}?maxwidth=400`
    : "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgdmlld0JveD0iMCAwIDQwMCAzMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI0MDAiIGhlaWdodD0iMzAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNzUgMTIwSDIyNVYxNDBIMjA1VjE2MEgxOTVWMTQwSDE3NVYxMjBaIiBmaWxsPSIjOUNBM0FGIi8+CjxwYXRoIGQ9Ik0xNjAgMTgwSDI0MFYyMDBIMTYwVjE4MFoiIGZpbGw9IiM5Q0EzQUYiLz4KPHN2Zz4K";

  // Generate restaurant URL
  const restaurantUrl = googlePlaceId
    ? `/restaurant/google/${googlePlaceId}`
    : `/restaurant/${id}`;

  // Format price level
  const priceDisplay = priceLevel ? "$".repeat(priceLevel) : null;

  // Format cuisine array
  const cuisineDisplay = Array.isArray(cuisine) ? cuisine.join(", ") : cuisine;

  return (
    <Card className="group overflow-hidden hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
      {/* Restaurant Image */}
      <div className="relative h-48 overflow-hidden">
        <Image
          src={photoUrl}
          alt={name}
          fill
          className="object-cover transition-transform duration-300 group-hover:scale-105"
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
        />

        {/* Overlay Badges */}
        <div className="absolute top-3 left-3 flex flex-col gap-2">
          {verified && (
            <Badge className="bg-green-500 hover:bg-green-600 text-white">
              <CheckCircle className="h-3 w-3 mr-1" />
              Verified
            </Badge>
          )}
          {isOpen !== undefined && (
            <Badge variant={isOpen ? "default" : "secondary"}>
              <Clock className="h-3 w-3 mr-1" />
              {isOpen ? "Open" : "Closed"}
            </Badge>
          )}
        </div>

        {/* Price Level */}
        {priceDisplay && (
          <div className="absolute top-3 right-3">
            <Badge variant="outline" className="bg-white/90 text-foreground">
              {priceDisplay}
            </Badge>
          </div>
        )}

        {/* Quick Actions */}
        <div className="absolute bottom-3 right-3 flex gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <Button size="sm" variant="secondary" className="h-8 w-8 p-0">
            <Heart className="h-4 w-4" />
          </Button>
          <Button size="sm" variant="secondary" className="h-8 w-8 p-0">
            <Share2 className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Restaurant Info */}
      <CardContent className="p-4">
        <div className="space-y-3">
          {/* Name and Rating */}
          <div className="flex items-start justify-between gap-2">
            <h3 className="font-semibold text-lg leading-tight line-clamp-2 group-hover:text-primary transition-colors">
              {name}
            </h3>
            {rating && (
              <div className="flex items-center gap-1 text-sm font-medium shrink-0">
                <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                <span>{rating.toFixed(1)}</span>
              </div>
            )}
          </div>

          {/* Cuisine */}
          {cuisineDisplay && (
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="text-xs">
                {cuisineDisplay}
              </Badge>
            </div>
          )}

          {/* Description */}
          {description && (
            <p className="text-sm text-muted-foreground line-clamp-2">
              {description}
            </p>
          )}

          {/* Location */}
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <MapPin className="h-4 w-4 shrink-0" />
            <span className="line-clamp-1">{city ? `${city}` : address}</span>
          </div>

          {/* Contact Info */}
          <div className="flex items-center gap-4 text-sm text-muted-foreground">
            {phone && (
              <div className="flex items-center gap-1">
                <Phone className="h-3 w-3" />
                <span className="text-xs">{phone}</span>
              </div>
            )}
            {website && (
              <div className="flex items-center gap-1">
                <ExternalLink className="h-3 w-3" />
                <span className="text-xs">Website</span>
              </div>
            )}
          </div>
        </div>
      </CardContent>

      {/* Card Footer */}
      <CardFooter className="p-4 pt-0">
        <div className="flex gap-2 w-full">
          <Button asChild className="flex-1">
            <Link href={restaurantUrl}>View Details</Link>
          </Button>

          {website && (
            <Button variant="outline" size="sm" asChild>
              <a
                href={website}
                target="_blank"
                rel="noopener noreferrer"
                className="px-3"
              >
                <ExternalLink className="h-4 w-4" />
              </a>
            </Button>
          )}

          {phone && (
            <Button variant="outline" size="sm" asChild>
              <a href={`tel:${phone}`} className="px-3">
                <Phone className="h-4 w-4" />
              </a>
            </Button>
          )}
        </div>
      </CardFooter>
    </Card>
  );
}

/**
 * UTILITY STYLES
 *
 * Add these to your global CSS if not already present:
 *
 * .line-clamp-1 {
 *   overflow: hidden;
 *   display: -webkit-box;
 *   -webkit-box-orient: vertical;
 *   -webkit-line-clamp: 1;
 * }
 *
 * .line-clamp-2 {
 *   overflow: hidden;
 *   display: -webkit-box;
 *   -webkit-box-orient: vertical;
 *   -webkit-line-clamp: 2;
 * }
 */
