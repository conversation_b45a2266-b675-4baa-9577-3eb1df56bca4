"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "../../components/ui/button";
import { Input } from "../../components/ui/input";
import { Card, CardContent } from "../../components/ui/card";
import { Badge } from "../../components/ui/badge";
import { Search, MapPin, Plus } from "lucide-react";

export default function SearchForm({
  initialQuery = "",
  initialLocation = "",
}) {
  const [query, setQuery] = useState(initialQuery);
  const [location, setLocation] = useState(initialLocation);
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);

    const params = new URLSearchParams();
    if (query.trim()) params.set("q", query.trim());
    if (location.trim()) params.set("location", location.trim());

    router.push(`/?${params.toString()}`);
    setIsLoading(false);
  };

  const handleAddRestaurant = () => {
    router.push("/add-restaurant");
  };

  const popularSearches = [
    "Pakistani",
    "Middle Eastern",
    "Turkish",
    "Indian",
    "Mediterranean",
    "Halal Pizza",
    "Halal Burgers",
    "Kebab",
  ];

  const popularLocations = [
    "New York, NY",
    "Los Angeles, CA",
    "Chicago, IL",
    "Houston, TX",
    "Toronto, ON",
    "London, UK",
  ];

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardContent className="p-6">
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Search Query Input */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                type="text"
                placeholder="Search for cuisine, restaurant name..."
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                className="pl-10"
              />
            </div>

            {/* Location Input */}
            <div className="relative">
              <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                type="text"
                placeholder="Enter city, address, or zip code..."
                value={location}
                onChange={(e) => setLocation(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <Button
              type="submit"
              disabled={isLoading}
              className="flex-1 sm:flex-none"
            >
              {isLoading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Searching...
                </>
              ) : (
                <>
                  <Search className="mr-2 h-4 w-4" />
                  Search Restaurants
                </>
              )}
            </Button>

            <Button
              type="button"
              variant="outline"
              onClick={handleAddRestaurant}
              className="flex-1 sm:flex-none"
            >
              <Plus className="mr-2 h-4 w-4" />
              Add Restaurant
            </Button>
          </div>
        </form>

        {/* Popular Searches */}
        {!query && !location && (
          <div className="mt-6 space-y-4">
            <div>
              <h3 className="text-sm font-medium text-muted-foreground mb-2">
                Popular Cuisines
              </h3>
              <div className="flex flex-wrap gap-2">
                {popularSearches.map((search) => (
                  <Badge
                    key={search}
                    variant="secondary"
                    className="cursor-pointer hover:bg-primary hover:text-primary-foreground transition-colors"
                    onClick={() => setQuery(search)}
                  >
                    {search}
                  </Badge>
                ))}
              </div>
            </div>

            <div>
              <h3 className="text-sm font-medium text-muted-foreground mb-2">
                Popular Locations
              </h3>
              <div className="flex flex-wrap gap-2">
                {popularLocations.map((loc) => (
                  <Badge
                    key={loc}
                    variant="outline"
                    className="cursor-pointer hover:bg-muted transition-colors"
                    onClick={() => setLocation(loc)}
                  >
                    {loc}
                  </Badge>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Current Search Display */}
        {(query || location) && (
          <div className="mt-4 p-3 bg-muted rounded-lg">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2 text-sm">
                <span className="text-muted-foreground">Searching for:</span>
                {query && <Badge variant="default">{query}</Badge>}
                {location && <Badge variant="outline">📍 {location}</Badge>}
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  setQuery("");
                  setLocation("");
                  router.push("/");
                }}
              >
                Clear
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
