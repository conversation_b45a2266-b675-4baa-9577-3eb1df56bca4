"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { Button } from "../../components/ui/button";
import { Input } from "../../components/ui/input";
import { Label } from "../../components/ui/label";
import { Textarea } from "../../components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../components/ui/select";
import { Badge } from "../../components/ui/badge";
import { MapPin, Plus, X, Loader2 } from "lucide-react";
import { addRestaurant } from "../actions/restaurant-actions";

export default function AddRestaurantForm() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");
  const [cuisines, setCuisines] = useState([]);
  const [newCuisine, setNewCuisine] = useState("");
  const router = useRouter();

  const popularCuisines = [
    "Pakistani",
    "Indian",
    "Middle Eastern",
    "Turkish",
    "Mediterranean",
    "Lebanese",
    "Moroccan",
    "Egyptian",
    "Persian",
    "Afghan",
    "Bangladeshi",
    "Malaysian",
    "Indonesian",
    "American",
    "Italian",
    "Chinese",
  ];

  const handleAddCuisine = (cuisine) => {
    if (cuisine && !cuisines.includes(cuisine)) {
      setCuisines([...cuisines, cuisine]);
    }
    setNewCuisine("");
  };

  const handleRemoveCuisine = (cuisine) => {
    setCuisines(cuisines.filter((c) => c !== cuisine));
  };

  const handleSubmit = async (formData) => {
    setIsSubmitting(true);
    setError("");
    setSuccess("");

    try {
      // Add cuisines to form data
      formData.set("cuisine", cuisines.join(","));

      const result = await addRestaurant(formData);

      if (result.success) {
        setSuccess(result.message);
        // Redirect to home page after 2 seconds
        setTimeout(() => {
          router.push("/");
        }, 2000);
      } else {
        setError(result.error);
      }
    } catch (err) {
      setError("An unexpected error occurred. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  if (success) {
    return (
      <div className="text-center py-8">
        <div className="text-green-600 text-6xl mb-4">✅</div>
        <h3 className="text-xl font-semibold text-green-600 mb-2">
          Restaurant Added Successfully!
        </h3>
        <p className="text-muted-foreground mb-4">{success}</p>
        <p className="text-sm text-muted-foreground">
          Redirecting you back to the main page...
        </p>
      </div>
    );
  }

  return (
    <form action={handleSubmit} className="space-y-6">
      {error && (
        <div className="p-4 border border-red-200 bg-red-50 rounded-lg text-red-600 text-sm">
          {error}
        </div>
      )}

      {/* Basic Information */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Basic Information</h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="name">Restaurant Name *</Label>
            <Input
              id="name"
              name="name"
              required
              placeholder="Enter restaurant name"
            />
          </div>

          <div>
            <Label htmlFor="phone">Phone Number</Label>
            <Input
              id="phone"
              name="phone"
              type="tel"
              placeholder="+****************"
            />
          </div>
        </div>

        <div>
          <Label htmlFor="description">Description</Label>
          <Textarea
            id="description"
            name="description"
            placeholder="Brief description of the restaurant, specialties, atmosphere..."
            rows={3}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="website">Website</Label>
            <Input
              id="website"
              name="website"
              type="url"
              placeholder="https://restaurant-website.com"
            />
          </div>

          <div>
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              name="email"
              type="email"
              placeholder="<EMAIL>"
            />
          </div>
        </div>
      </div>

      {/* Location Information */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Location</h3>

        <div>
          <Label htmlFor="address">Full Address *</Label>
          <Input
            id="address"
            name="address"
            required
            placeholder="123 Main St, City, State, ZIP"
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <Label htmlFor="city">City *</Label>
            <Input id="city" name="city" required placeholder="New York" />
          </div>

          <div>
            <Label htmlFor="state">State/Province</Label>
            <Input id="state" name="state" placeholder="NY" />
          </div>

          <div>
            <Label htmlFor="zipCode">ZIP/Postal Code</Label>
            <Input id="zipCode" name="zipCode" placeholder="10001" />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="latitude">Latitude *</Label>
            <Input
              id="latitude"
              name="latitude"
              type="number"
              step="any"
              required
              placeholder="40.7128"
            />
          </div>

          <div>
            <Label htmlFor="longitude">Longitude *</Label>
            <Input
              id="longitude"
              name="longitude"
              type="number"
              step="any"
              required
              placeholder="-74.0060"
            />
          </div>
        </div>

        <div className="text-sm text-muted-foreground">
          <MapPin className="inline w-4 h-4 mr-1" />
          You can find coordinates using Google Maps or other mapping services
        </div>
      </div>

      {/* Restaurant Details */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Restaurant Details</h3>

        <div>
          <Label>Cuisine Types</Label>
          <div className="space-y-3">
            {/* Selected Cuisines */}
            {cuisines.length > 0 && (
              <div className="flex flex-wrap gap-2">
                {cuisines.map((cuisine) => (
                  <Badge
                    key={cuisine}
                    variant="default"
                    className="flex items-center gap-1"
                  >
                    {cuisine}
                    <X
                      className="w-3 h-3 cursor-pointer hover:text-red-500"
                      onClick={() => handleRemoveCuisine(cuisine)}
                    />
                  </Badge>
                ))}
              </div>
            )}

            {/* Add Custom Cuisine */}
            <div className="flex gap-2">
              <Input
                value={newCuisine}
                onChange={(e) => setNewCuisine(e.target.value)}
                placeholder="Add cuisine type..."
                onKeyPress={(e) => {
                  if (e.key === "Enter") {
                    e.preventDefault();
                    handleAddCuisine(newCuisine);
                  }
                }}
              />
              <Button
                type="button"
                variant="outline"
                onClick={() => handleAddCuisine(newCuisine)}
                disabled={!newCuisine.trim()}
              >
                <Plus className="w-4 h-4" />
              </Button>
            </div>

            {/* Popular Cuisines */}
            <div>
              <p className="text-sm text-muted-foreground mb-2">
                Popular cuisines:
              </p>
              <div className="flex flex-wrap gap-2">
                {popularCuisines.map((cuisine) => (
                  <Badge
                    key={cuisine}
                    variant="outline"
                    className="cursor-pointer hover:bg-primary hover:text-primary-foreground"
                    onClick={() => handleAddCuisine(cuisine)}
                  >
                    {cuisine}
                  </Badge>
                ))}
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="priceLevel">Price Level</Label>
            <Select name="priceLevel">
              <SelectTrigger>
                <SelectValue placeholder="Select price range" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="1">$ - Inexpensive</SelectItem>
                <SelectItem value="2">$$ - Moderate</SelectItem>
                <SelectItem value="3">$$$ - Expensive</SelectItem>
                <SelectItem value="4">$$$$ - Very Expensive</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="halalCertification">Halal Certification</Label>
            <Input
              id="halalCertification"
              name="halalCertification"
              placeholder="e.g., ISNA, HMA, Local Islamic Center"
            />
          </div>
        </div>

        <div>
          <Label htmlFor="googlePlaceId">Google Place ID (Optional)</Label>
          <Input
            id="googlePlaceId"
            name="googlePlaceId"
            placeholder="If you know the Google Place ID"
          />
          <p className="text-xs text-muted-foreground mt-1">
            This helps us link with Google Places data while respecting their
            terms
          </p>
        </div>
      </div>

      {/* Submit Button */}
      <div className="pt-4">
        <Button type="submit" disabled={isSubmitting} className="w-full">
          {isSubmitting ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Adding Restaurant...
            </>
          ) : (
            "Add Restaurant"
          )}
        </Button>
      </div>
    </form>
  );
}
