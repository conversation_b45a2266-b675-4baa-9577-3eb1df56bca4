/**
 * GOOGLE PLACES SEARCH COMPONENT
 *
 * This client component provides an interface for searching and adding restaurants
 * from Google Places API. It's used in the restaurant addition workflow to allow
 * users to find restaurants via Google and add them to the local directory.
 *
 * Key Features:
 * - Real-time Google Places search
 * - Restaurant preview cards with Google data
 * - One-click restaurant addition to local database
 * - Loading states and error handling
 * - Duplicate detection and prevention
 * - Success feedback and navigation
 *
 * Component Architecture:
 * - Client Component for interactive search
 * - State management for search results and UI states
 * - Integration with Server Actions for data mutations
 * - Responsive design with mobile-first approach
 *
 * User Workflow:
 * 1. User enters search query (restaurant name + location)
 * 2. Component calls Google Places API via Server Action
 * 3. Results displayed as preview cards
 * 4. User clicks "Add to Directory" on desired restaurant
 * 5. Restaurant data imported to local database
 * 6. Success feedback and optional navigation to restaurant page
 *
 * @see app/actions/restaurant-actions.js for Server Action integration
 */

"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { Button } from "../../components/ui/button";
import { Input } from "../../components/ui/input";
import { Card, CardContent } from "../../components/ui/card";
import { Badge } from "../../components/ui/badge";
import {
  Search,
  MapPin,
  Star,
  Clock,
  Plus,
  Loader2,
  CheckCircle,
  AlertCircle,
} from "lucide-react";
import Image from "next/image";
import {
  searchGooglePlaces,
  addRestaurantFromGoogle,
} from "../actions/restaurant-actions";

export default function GooglePlacesSearch() {
  const [query, setQuery] = useState("");
  const [location, setLocation] = useState("");
  const [isSearching, setIsSearching] = useState(false);
  const [searchResults, setSearchResults] = useState([]);
  const [error, setError] = useState("");
  const [addingRestaurants, setAddingRestaurants] = useState(new Set());
  const [addedRestaurants, setAddedRestaurants] = useState(new Set());
  const router = useRouter();

  const handleSearch = async (e) => {
    e.preventDefault();
    if (!query.trim()) return;

    setIsSearching(true);
    setError("");
    setSearchResults([]);

    try {
      const result = await searchGooglePlaces(query, location);

      if (result.success) {
        setSearchResults(result.data);
        if (result.data.length === 0) {
          setError(
            "No restaurants found. Try different search terms or location."
          );
        }
      } else {
        setError(result.error || "Failed to search restaurants");
      }
    } catch (err) {
      setError("An error occurred while searching. Please try again.");
    } finally {
      setIsSearching(false);
    }
  };

  const handleAddRestaurant = async (restaurant) => {
    const restaurantId = restaurant.placeId;
    setAddingRestaurants((prev) => new Set([...prev, restaurantId]));
    setError("");

    try {
      const result = await addRestaurantFromGoogle(restaurant);

      if (result.success) {
        setAddedRestaurants((prev) => new Set([...prev, restaurantId]));
        // Show success message briefly, then redirect
        setTimeout(() => {
          router.push("/");
        }, 2000);
      } else {
        setError(result.error || "Failed to add restaurant");
      }
    } catch (err) {
      setError(
        "An error occurred while adding the restaurant. Please try again."
      );
    } finally {
      setAddingRestaurants((prev) => {
        const newSet = new Set(prev);
        newSet.delete(restaurantId);
        return newSet;
      });
    }
  };

  return (
    <div className="space-y-6">
      {/* Search Form */}
      <form onSubmit={handleSearch} className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              type="text"
              placeholder="Restaurant name (e.g., 'Halal Guys', 'Pakistani restaurant')"
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              className="pl-10"
              required
            />
          </div>

          <div className="relative">
            <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              type="text"
              placeholder="Location (e.g., 'New York, NY', 'Toronto')"
              value={location}
              onChange={(e) => setLocation(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        <Button
          type="submit"
          disabled={isSearching || !query.trim()}
          className="w-full md:w-auto"
        >
          {isSearching ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Searching Google Places...
            </>
          ) : (
            <>
              <Search className="mr-2 h-4 w-4" />
              Search Restaurants
            </>
          )}
        </Button>
      </form>

      {/* Error Message */}
      {error && (
        <div className="p-4 border border-red-200 bg-red-50 rounded-lg text-red-600 text-sm flex items-start">
          <AlertCircle className="h-4 w-4 mr-2 mt-0.5 flex-shrink-0" />
          {error}
        </div>
      )}

      {/* Search Results */}
      {searchResults.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">
            Found {searchResults.length} restaurant
            {searchResults.length !== 1 ? "s" : ""}
          </h3>

          <div className="grid gap-4">
            {searchResults.map((restaurant) => (
              <RestaurantSearchCard
                key={restaurant.placeId}
                restaurant={restaurant}
                onAdd={handleAddRestaurant}
                isAdding={addingRestaurants.has(restaurant.placeId)}
                isAdded={addedRestaurants.has(restaurant.placeId)}
              />
            ))}
          </div>
        </div>
      )}

      {/* Search Tips */}
      {searchResults.length === 0 && !isSearching && !error && (
        <Card className="bg-muted/50">
          <CardContent className="p-4">
            <h4 className="font-medium mb-2">Search Tips:</h4>
            <ul className="text-sm text-muted-foreground space-y-1">
              <li>
                • Include "halal" in your search (e.g., "halal pizza", "halal
                restaurant")
              </li>
              <li>• Try specific restaurant names if you know them</li>
              <li>• Include the city or neighborhood for better results</li>
              <li>
                • Use cuisine types like "Pakistani restaurant", "Middle Eastern
                food"
              </li>
            </ul>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

function RestaurantSearchCard({ restaurant, onAdd, isAdding, isAdded }) {
  const { placeId, name, address, rating, priceLevel, isOpen, photos, types } =
    restaurant;

  // Get the first photo for display
  const primaryPhoto = photos?.[0];
  const photoUrl = primaryPhoto?.photoReference
    ? `/api/photo/${primaryPhoto.photoReference}?maxwidth=300`
    : null;

  return (
    <Card className="overflow-hidden">
      <div className="flex flex-col md:flex-row">
        {/* Restaurant Image */}
        <div className="relative w-full md:w-32 h-32 bg-muted">
          {photoUrl ? (
            <Image
              src={photoUrl}
              alt={name}
              fill
              className="object-cover"
              sizes="(max-width: 768px) 100vw, 128px"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center text-muted-foreground">
              <div className="text-center">
                <div className="text-2xl mb-1">🍽️</div>
                <div className="text-xs">No Image</div>
              </div>
            </div>
          )}
        </div>

        {/* Restaurant Details */}
        <div className="flex-1 p-4">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <h4 className="font-semibold text-lg mb-1">{name}</h4>

              <div className="flex items-center text-muted-foreground text-sm mb-2">
                <MapPin className="w-3 h-3 mr-1" />
                <span>{address}</span>
              </div>

              {/* Restaurant Info */}
              <div className="flex items-center space-x-4 mb-3">
                {rating && (
                  <div className="flex items-center">
                    <Star className="w-4 h-4 fill-yellow-400 text-yellow-400 mr-1" />
                    <span className="text-sm font-medium">
                      {rating.toFixed(1)}
                    </span>
                  </div>
                )}

                {priceLevel && (
                  <div className="flex items-center">
                    <span className="text-sm font-medium">
                      {"$".repeat(priceLevel)}
                      <span className="text-muted-foreground">
                        {"$".repeat(4 - priceLevel)}
                      </span>
                    </span>
                  </div>
                )}

                {isOpen !== null && (
                  <div className="flex items-center text-sm">
                    <Clock className="w-3 h-3 mr-1" />
                    <span
                      className={isOpen ? "text-green-600" : "text-red-600"}
                    >
                      {isOpen ? "Open" : "Closed"}
                    </span>
                  </div>
                )}
              </div>

              {/* Restaurant Types */}
              {types && types.length > 0 && (
                <div className="flex flex-wrap gap-1 mb-3">
                  {types.slice(0, 3).map((type) => (
                    <Badge key={type} variant="outline" className="text-xs">
                      {type.replace(/_/g, " ").toLowerCase()}
                    </Badge>
                  ))}
                </div>
              )}
            </div>

            {/* Add Button */}
            <div className="ml-4">
              {isAdded ? (
                <div className="flex items-center text-green-600 text-sm">
                  <CheckCircle className="w-4 h-4 mr-1" />
                  Added!
                </div>
              ) : (
                <Button
                  onClick={() => onAdd(restaurant)}
                  disabled={isAdding}
                  size="sm"
                >
                  {isAdding ? (
                    <>
                      <Loader2 className="mr-1 h-3 w-3 animate-spin" />
                      Adding...
                    </>
                  ) : (
                    <>
                      <Plus className="mr-1 h-3 w-3" />
                      Add to Directory
                    </>
                  )}
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
}
