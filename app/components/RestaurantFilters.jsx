/**
 * RESTAURANT FILTERS COMPONENT
 * 
 * This component provides comprehensive filtering options for the restaurant listing.
 * It handles filter state management and URL parameter synchronization.
 * 
 * Key Features:
 * - Search input with debouncing
 * - Cuisine type selection
 * - Price level filtering
 * - Verification status filter
 * - City/location filtering
 * - Sort options
 * - Filter reset functionality
 * - URL parameter synchronization
 * 
 * @see app/restaurants/page.jsx for usage
 */

'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card';
import { Input } from '../../components/ui/input';
import { Button } from '../../components/ui/button';
import { Label } from '../../components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../components/ui/select';
import { Checkbox } from '../../components/ui/checkbox';
import { Badge } from '../../components/ui/badge';
import { Separator } from '../../components/ui/separator';
import { 
  Search, 
  Filter, 
  X, 
  MapPin, 
  DollarSign, 
  CheckCircle, 
  ChefHat,
  SortAsc,
  RotateCcw
} from 'lucide-react';

// Cuisine options
const CUISINE_OPTIONS = [
  'Middle Eastern',
  'South Asian',
  'Mediterranean',
  'Turkish',
  'Pakistani',
  'Indian',
  'Lebanese',
  'Moroccan',
  'Persian',
  'Afghan',
  'Malaysian',
  'Indonesian',
  'American',
  'Fast Food',
  'Pizza',
  'Burgers',
  'Other'
];

// Price level options
const PRICE_LEVELS = [
  { value: 1, label: '$', description: 'Budget-friendly' },
  { value: 2, label: '$$', description: 'Moderate' },
  { value: 3, label: '$$$', description: 'Expensive' },
  { value: 4, label: '$$$$', description: 'Very Expensive' }
];

// Sort options
const SORT_OPTIONS = [
  { value: 'name', label: 'Name' },
  { value: 'rating', label: 'Rating' },
  { value: 'createdAt', label: 'Date Added' },
  { value: 'city', label: 'Location' }
];

export default function RestaurantFilters({ initialFilters, totalResults }) {
  const router = useRouter();
  const searchParams = useSearchParams();
  
  // Filter state
  const [filters, setFilters] = useState(initialFilters);
  const [searchInput, setSearchInput] = useState(initialFilters.search || '');

  // Debounced search
  useEffect(() => {
    const timer = setTimeout(() => {
      if (searchInput !== filters.search) {
        updateFilter('search', searchInput);
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [searchInput]);

  // Update URL parameters
  const updateFilter = useCallback((key, value) => {
    const newFilters = { ...filters, [key]: value, page: 1 }; // Reset to page 1 when filtering
    setFilters(newFilters);

    // Build URL parameters
    const params = new URLSearchParams();
    Object.entries(newFilters).forEach(([k, v]) => {
      if (v !== null && v !== undefined && v !== '') {
        params.set(k, v.toString());
      }
    });

    router.push(`/restaurants?${params.toString()}`);
  }, [filters, router]);

  // Clear all filters
  const clearFilters = () => {
    setFilters({
      search: '',
      cuisine: '',
      priceLevel: null,
      verified: null,
      city: '',
      sortBy: 'name',
      sortOrder: 'asc',
      page: 1,
      limit: 12
    });
    setSearchInput('');
    router.push('/restaurants');
  };

  // Count active filters
  const activeFiltersCount = Object.entries(filters).filter(([key, value]) => {
    if (key === 'page' || key === 'limit' || key === 'sortBy' || key === 'sortOrder') return false;
    return value !== null && value !== undefined && value !== '';
  }).length;

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filters
            {activeFiltersCount > 0 && (
              <Badge variant="secondary" className="ml-2">
                {activeFiltersCount}
              </Badge>
            )}
          </CardTitle>
          {activeFiltersCount > 0 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={clearFilters}
              className="text-muted-foreground hover:text-foreground"
            >
              <RotateCcw className="h-4 w-4 mr-1" />
              Clear
            </Button>
          )}
        </div>
        <p className="text-sm text-muted-foreground">
          {totalResults} restaurants found
        </p>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Search */}
        <div className="space-y-2">
          <Label htmlFor="search" className="flex items-center gap-2">
            <Search className="h-4 w-4" />
            Search
          </Label>
          <div className="relative">
            <Input
              id="search"
              placeholder="Restaurant name, cuisine, location..."
              value={searchInput}
              onChange={(e) => setSearchInput(e.target.value)}
              className="pr-8"
            />
            {searchInput && (
              <Button
                variant="ghost"
                size="sm"
                className="absolute right-1 top-1/2 -translate-y-1/2 h-6 w-6 p-0"
                onClick={() => setSearchInput('')}
              >
                <X className="h-3 w-3" />
              </Button>
            )}
          </div>
        </div>

        <Separator />

        {/* Cuisine Filter */}
        <div className="space-y-2">
          <Label className="flex items-center gap-2">
            <ChefHat className="h-4 w-4" />
            Cuisine Type
          </Label>
          <Select
            value={filters.cuisine || ''}
            onValueChange={(value) => updateFilter('cuisine', value || '')}
          >
            <SelectTrigger>
              <SelectValue placeholder="All cuisines" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">All cuisines</SelectItem>
              {CUISINE_OPTIONS.map((cuisine) => (
                <SelectItem key={cuisine} value={cuisine}>
                  {cuisine}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Price Level Filter */}
        <div className="space-y-2">
          <Label className="flex items-center gap-2">
            <DollarSign className="h-4 w-4" />
            Price Range
          </Label>
          <Select
            value={filters.priceLevel?.toString() || ''}
            onValueChange={(value) => updateFilter('priceLevel', value ? parseInt(value) : null)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Any price" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">Any price</SelectItem>
              {PRICE_LEVELS.map((level) => (
                <SelectItem key={level.value} value={level.value.toString()}>
                  <div className="flex items-center gap-2">
                    <span className="font-mono">{level.label}</span>
                    <span className="text-muted-foreground">{level.description}</span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Verification Status */}
        <div className="space-y-2">
          <Label className="flex items-center gap-2">
            <CheckCircle className="h-4 w-4" />
            Verification Status
          </Label>
          <Select
            value={filters.verified === null ? '' : filters.verified.toString()}
            onValueChange={(value) => updateFilter('verified', value === '' ? null : value === 'true')}
          >
            <SelectTrigger>
              <SelectValue placeholder="All restaurants" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">All restaurants</SelectItem>
              <SelectItem value="true">Verified only</SelectItem>
              <SelectItem value="false">Unverified only</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* City Filter */}
        <div className="space-y-2">
          <Label htmlFor="city" className="flex items-center gap-2">
            <MapPin className="h-4 w-4" />
            City
          </Label>
          <Input
            id="city"
            placeholder="Enter city name"
            value={filters.city || ''}
            onChange={(e) => updateFilter('city', e.target.value)}
          />
        </div>

        <Separator />

        {/* Sort Options */}
        <div className="space-y-2">
          <Label className="flex items-center gap-2">
            <SortAsc className="h-4 w-4" />
            Sort By
          </Label>
          <div className="grid grid-cols-2 gap-2">
            <Select
              value={filters.sortBy || 'name'}
              onValueChange={(value) => updateFilter('sortBy', value)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {SORT_OPTIONS.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            <Select
              value={filters.sortOrder || 'asc'}
              onValueChange={(value) => updateFilter('sortOrder', value)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="asc">A-Z</SelectItem>
                <SelectItem value="desc">Z-A</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
