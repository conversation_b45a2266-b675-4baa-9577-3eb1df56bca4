import { searchRestaurants } from "../actions/restaurant-actions";
import { <PERSON>, CardContent, CardHeader } from "../../components/ui/card";
import { Badge } from "../../components/ui/badge";
import { Button } from "../../components/ui/button";
import {
  Star,
  MapPin,
  Clock,
  Phone,
  Globe,
  CheckCircle,
  Search,
  Plus,
} from "lucide-react";
import Link from "next/link";
import Image from "next/image";

export default async function RestaurantList({ query, location }) {
  // If no search parameters, show welcome message
  if (!query && !location) {
    return <WelcomeMessage />;
  }

  const result = await searchRestaurants(query, location);

  if (!result.success) {
    return (
      <div className="text-center py-12">
        <div className="text-muted-foreground mb-4">
          <Search className="mx-auto h-12 w-12 mb-4" />
          <p>Sorry, we encountered an error while searching.</p>
          <p className="text-sm">{result.error}</p>
        </div>
      </div>
    );
  }

  if (result.data.length === 0) {
    return <NoResultsMessage query={query} location={location} />;
  }

  return (
    <div className="space-y-6">
      {/* Results Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-semibold">
          Found {result.total} halal restaurant{result.total !== 1 ? "s" : ""}
        </h2>
        <div className="text-sm text-muted-foreground">
          {query && <span>for "{query}"</span>}
          {query && location && <span> </span>}
          {location && <span>in {location}</span>}
        </div>
      </div>

      {/* Restaurant Cards */}
      <div className="grid gap-6">
        {result.data.map((restaurant) => (
          <RestaurantCard
            key={`${restaurant.source}-${restaurant.id}`}
            restaurant={restaurant}
          />
        ))}
      </div>
    </div>
  );
}

function RestaurantCard({ restaurant }) {
  const {
    id,
    name,
    address,
    rating,
    priceLevel,
    isOpen,
    photos,
    source,
    googlePlaceId,
    isVerified,
    reviewCount,
  } = restaurant;

  // Get the first photo for display
  const primaryPhoto = photos?.[0];
  const photoUrl =
    primaryPhoto?.url ||
    (primaryPhoto?.photoReference
      ? `/api/photo/${primaryPhoto.photoReference}`
      : null);

  const detailsUrl =
    source === "google"
      ? `/restaurant/google/${googlePlaceId}`
      : `/restaurant/${id}`;

  return (
    <Card className="overflow-hidden hover:shadow-lg transition-shadow">
      <div className="flex flex-col md:flex-row">
        {/* Restaurant Image */}
        <div className="relative w-full md:w-48 h-48 md:h-auto bg-muted">
          {photoUrl ? (
            <Image
              src={photoUrl}
              alt={name}
              fill
              className="object-cover"
              sizes="(max-width: 768px) 100vw, 192px"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center text-muted-foreground">
              <div className="text-center">
                <div className="text-4xl mb-2">🍽️</div>
                <div className="text-sm">No Image</div>
              </div>
            </div>
          )}
          {isVerified && (
            <div className="absolute top-2 left-2">
              <Badge className="bg-green-500 hover:bg-green-600">
                <CheckCircle className="w-3 h-3 mr-1" />
                Verified
              </Badge>
            </div>
          )}
          {source === "google" && (
            <div className="absolute top-2 right-2">
              <Badge variant="outline" className="bg-white/90">
                Google
              </Badge>
            </div>
          )}
        </div>

        {/* Restaurant Details */}
        <div className="flex-1">
          <CardHeader className="pb-3">
            <div className="flex items-start justify-between">
              <div>
                <h3 className="text-xl font-semibold mb-1">{name}</h3>
                <div className="flex items-center text-muted-foreground text-sm mb-2">
                  <MapPin className="w-4 h-4 mr-1" />
                  <span>{address}</span>
                </div>
              </div>

              {/* Rating and Status */}
              <div className="text-right">
                {rating && (
                  <div className="flex items-center mb-1">
                    <Star className="w-4 h-4 fill-yellow-400 text-yellow-400 mr-1" />
                    <span className="font-medium">{rating.toFixed(1)}</span>
                    {reviewCount > 0 && (
                      <span className="text-muted-foreground text-sm ml-1">
                        ({reviewCount})
                      </span>
                    )}
                  </div>
                )}

                {isOpen !== null && (
                  <div className="flex items-center text-sm">
                    <Clock className="w-4 h-4 mr-1" />
                    <span
                      className={isOpen ? "text-green-600" : "text-red-600"}
                    >
                      {isOpen ? "Open" : "Closed"}
                    </span>
                  </div>
                )}
              </div>
            </div>
          </CardHeader>

          <CardContent className="pt-0">
            <div className="flex items-center justify-between">
              {/* Price Level */}
              <div className="flex items-center space-x-4">
                {priceLevel && (
                  <div className="flex items-center">
                    <span className="text-sm text-muted-foreground mr-1">
                      Price:
                    </span>
                    <span className="font-medium">
                      {"$".repeat(priceLevel)}
                      <span className="text-muted-foreground">
                        {"$".repeat(4 - priceLevel)}
                      </span>
                    </span>
                  </div>
                )}

                <Badge
                  variant="secondary"
                  className="bg-green-100 text-green-800"
                >
                  🥩 Halal
                </Badge>
              </div>

              {/* Action Button */}
              <Link href={detailsUrl}>
                <Button variant="outline">View Details</Button>
              </Link>
            </div>
          </CardContent>
        </div>
      </div>
    </Card>
  );
}

function WelcomeMessage() {
  return (
    <div className="text-center py-16">
      <div className="max-w-2xl mx-auto">
        <div className="text-6xl mb-6">🕌</div>
        <h2 className="text-3xl font-bold mb-4">
          Welcome to Halal Restaurant Directory
        </h2>
        <p className="text-lg text-muted-foreground mb-8">
          Discover authentic halal restaurants in your area. Use the search
          above to find restaurants by cuisine type, name, or location.
        </p>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-left">
          <div className="p-4 border rounded-lg">
            <Search className="h-8 w-8 text-primary mb-3" />
            <h3 className="font-semibold mb-2">Search & Discover</h3>
            <p className="text-sm text-muted-foreground">
              Find halal restaurants by cuisine, location, or restaurant name
              using our powerful search.
            </p>
          </div>
          <div className="p-4 border rounded-lg">
            <Plus className="h-8 w-8 text-primary mb-3" />
            <h3 className="font-semibold mb-2">Add Restaurants</h3>
            <p className="text-sm text-muted-foreground">
              Help grow our community by adding your favorite halal restaurants
              to the directory.
            </p>
          </div>
          <div className="p-4 border rounded-lg">
            <CheckCircle className="h-8 w-8 text-primary mb-3" />
            <h3 className="font-semibold mb-2">Verified Listings</h3>
            <p className="text-sm text-muted-foreground">
              Look for verified restaurants that have been confirmed by our
              community.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

function NoResultsMessage({ query, location }) {
  return (
    <div className="text-center py-12">
      <div className="text-muted-foreground mb-6">
        <Search className="mx-auto h-16 w-16 mb-4" />
        <h3 className="text-xl font-semibold mb-2">No restaurants found</h3>
        <p>
          We couldn't find any halal restaurants
          {query && <span> matching "{query}"</span>}
          {location && <span> in {location}</span>}.
        </p>
      </div>

      <div className="space-y-4">
        <p className="text-sm text-muted-foreground">Try:</p>
        <ul className="text-sm text-muted-foreground space-y-1">
          <li>• Searching with different keywords</li>
          <li>• Expanding your search area</li>
          <li>• Adding the restaurant if you know of one in this area</li>
        </ul>

        <div className="mt-6">
          <Link href="/add-restaurant">
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Add a Restaurant
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );
}
