"use client";

import { useState } from "react";
import Link from "next/link";
import Image from "next/image";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "../../components/ui/card";
import { Badge } from "../../components/ui/badge";
import { Button } from "../../components/ui/button";
import { Separator } from "../../components/ui/separator";
import {
  ArrowLeft,
  Star,
  MapPin,
  Phone,
  Globe,
  Clock,
  CheckCircle,
  Calendar,
  User,
  Camera,
  ExternalLink,
  Navigation,
} from "lucide-react";

export default function RestaurantDetailsPage({ restaurant }) {
  const [selectedPhotoIndex, setSelectedPhotoIndex] = useState(0);

  const {
    id,
    name,
    description,
    address,
    phone,
    website,
    email,
    latitude,
    longitude,
    city,
    state,
    country,
    zipCode,
    cuisine,
    priceLevel,
    isHalal,
    halalCertification,
    isVerified,
    openingHours,
    googlePlaceId,
    photos,
    reviews,
    addedBy,
    createdAt,
    source,
    googleData,
    localData,
    rating,
    reviewCount,
    isOpen,
  } = restaurant;

  // Combine photos from different sources
  const allPhotos = [];

  // Add local photos
  if (photos && photos.length > 0) {
    allPhotos.push(
      ...photos.map((photo) => ({
        url: photo.url,
        caption: photo.caption,
        source: "local",
      }))
    );
  }

  // Add Google photos if available
  if (googleData?.photos && googleData.photos.length > 0) {
    allPhotos.push(
      ...googleData.photos.map((photo) => ({
        url: photo.photoReference
          ? `/api/photo/${photo.photoReference}?maxwidth=800`
          : null,
        caption: null,
        source: "google",
      }))
    );
  }

  // Use Google data for additional info if available
  const displayRating = rating || googleData?.rating;
  const displayReviewCount = reviewCount || googleData?.reviewCount;
  const displayIsOpen = isOpen !== undefined ? isOpen : googleData?.isOpen;
  const displayPhone = phone || googleData?.phone;
  const displayWebsite = website || googleData?.website;

  const formatOpeningHours = (hours) => {
    if (!hours) return null;

    if (typeof hours === "string") {
      try {
        hours = JSON.parse(hours);
      } catch {
        return hours;
      }
    }

    if (Array.isArray(hours)) {
      return hours;
    }

    return null;
  };

  const openingHoursArray =
    formatOpeningHours(openingHours) || googleData?.openingHours;

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b bg-white/95 backdrop-blur supports-[backdrop-filter]:bg-white/60">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center space-x-4">
            <Link href="/">
              <Button variant="ghost" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Search
              </Button>
            </Link>
            <div className="flex items-center space-x-2">
              {isVerified && (
                <Badge className="bg-green-500 hover:bg-green-600">
                  <CheckCircle className="w-3 h-3 mr-1" />
                  Verified
                </Badge>
              )}
              {source === "google" && (
                <Badge variant="outline">Google Places</Badge>
              )}
              {localData && source === "google" && (
                <Badge variant="secondary">In Directory</Badge>
              )}
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          {/* Restaurant Header */}
          <div className="mb-8">
            <div className="flex flex-col lg:flex-row gap-8">
              {/* Photo Gallery */}
              <div className="lg:w-1/2">
                {allPhotos.length > 0 ? (
                  <div className="space-y-4">
                    {/* Main Photo */}
                    <div className="relative aspect-video rounded-lg overflow-hidden bg-muted">
                      <Image
                        src={
                          allPhotos[selectedPhotoIndex]?.url ||
                          "/placeholder-restaurant.jpg"
                        }
                        alt={name}
                        fill
                        className="object-cover"
                        sizes="(max-width: 1024px) 100vw, 50vw"
                      />
                      {allPhotos[selectedPhotoIndex]?.source === "google" && (
                        <div className="absolute bottom-2 right-2">
                          <Badge variant="outline" className="bg-white/90">
                            Google
                          </Badge>
                        </div>
                      )}
                    </div>

                    {/* Photo Thumbnails */}
                    {allPhotos.length > 1 && (
                      <div className="flex gap-2 overflow-x-auto">
                        {allPhotos.slice(0, 6).map((photo, index) => (
                          <button
                            key={index}
                            onClick={() => setSelectedPhotoIndex(index)}
                            className={`relative w-20 h-20 rounded-md overflow-hidden flex-shrink-0 border-2 ${
                              selectedPhotoIndex === index
                                ? "border-primary"
                                : "border-transparent"
                            }`}
                          >
                            <Image
                              src={photo.url || "/placeholder-restaurant.jpg"}
                              alt={`${name} photo ${index + 1}`}
                              fill
                              className="object-cover"
                              sizes="80px"
                            />
                          </button>
                        ))}
                        {allPhotos.length > 6 && (
                          <div className="w-20 h-20 rounded-md bg-muted flex items-center justify-center text-sm text-muted-foreground">
                            +{allPhotos.length - 6}
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="aspect-video rounded-lg bg-muted flex items-center justify-center">
                    <div className="text-center text-muted-foreground">
                      <Camera className="h-12 w-12 mx-auto mb-2" />
                      <p>No photos available</p>
                    </div>
                  </div>
                )}
              </div>

              {/* Restaurant Info */}
              <div className="lg:w-1/2 space-y-6">
                <div>
                  <h1 className="text-3xl font-bold mb-2">{name}</h1>
                  {description && (
                    <p className="text-muted-foreground text-lg">
                      {description}
                    </p>
                  )}
                </div>

                {/* Rating and Status */}
                <div className="flex items-center gap-4 flex-wrap">
                  {displayRating && (
                    <div className="flex items-center">
                      <Star className="w-5 h-5 fill-yellow-400 text-yellow-400 mr-1" />
                      <span className="font-semibold">{displayRating}</span>
                      {displayReviewCount && (
                        <span className="text-muted-foreground ml-1">
                          ({displayReviewCount} reviews)
                        </span>
                      )}
                    </div>
                  )}

                  {displayIsOpen !== undefined && (
                    <div className="flex items-center">
                      <Clock className="w-4 h-4 mr-1" />
                      <span
                        className={
                          displayIsOpen ? "text-green-600" : "text-red-600"
                        }
                      >
                        {displayIsOpen ? "Open" : "Closed"}
                      </span>
                    </div>
                  )}

                  <Badge className="bg-green-100 text-green-800">
                    🥩 Halal
                  </Badge>

                  {halalCertification && (
                    <Badge variant="outline">
                      Certified: {halalCertification}
                    </Badge>
                  )}
                </div>

                {/* Price Level */}
                {priceLevel && (
                  <div className="flex items-center">
                    <span className="text-sm text-muted-foreground mr-2">
                      Price Level:
                    </span>
                    <span className="font-medium">
                      {"$".repeat(priceLevel)}
                      <span className="text-muted-foreground">
                        {"$".repeat(4 - priceLevel)}
                      </span>
                    </span>
                  </div>
                )}

                {/* Cuisine Types */}
                {cuisine && cuisine.length > 0 && (
                  <div>
                    <h3 className="font-semibold mb-2">Cuisine</h3>
                    <div className="flex flex-wrap gap-2">
                      {cuisine.map((type, index) => (
                        <Badge key={index} variant="secondary">
                          {type}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Details Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Contact & Location */}
            <div className="lg:col-span-2 space-y-6">
              {/* Contact Information */}
              <Card>
                <CardHeader>
                  <CardTitle>Contact Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-start">
                    <MapPin className="w-5 h-5 mr-3 mt-0.5 text-muted-foreground" />
                    <div>
                      <p className="font-medium">Address</p>
                      <p className="text-muted-foreground">{address}</p>
                      {(city || state || zipCode) && (
                        <p className="text-muted-foreground">
                          {[city, state, zipCode].filter(Boolean).join(", ")}
                        </p>
                      )}
                    </div>
                  </div>

                  {displayPhone && (
                    <div className="flex items-center">
                      <Phone className="w-5 h-5 mr-3 text-muted-foreground" />
                      <div>
                        <p className="font-medium">Phone</p>
                        <a
                          href={`tel:${displayPhone}`}
                          className="text-primary hover:underline"
                        >
                          {displayPhone}
                        </a>
                      </div>
                    </div>
                  )}

                  {displayWebsite && (
                    <div className="flex items-center">
                      <Globe className="w-5 h-5 mr-3 text-muted-foreground" />
                      <div>
                        <p className="font-medium">Website</p>
                        <a
                          href={displayWebsite}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-primary hover:underline flex items-center"
                        >
                          Visit Website
                          <ExternalLink className="w-4 h-4 ml-1" />
                        </a>
                      </div>
                    </div>
                  )}

                  {email && (
                    <div className="flex items-center">
                      <User className="w-5 h-5 mr-3 text-muted-foreground" />
                      <div>
                        <p className="font-medium">Email</p>
                        <a
                          href={`mailto:${email}`}
                          className="text-primary hover:underline"
                        >
                          {email}
                        </a>
                      </div>
                    </div>
                  )}

                  {/* Directions Button */}
                  {latitude && longitude && (
                    <div className="pt-4">
                      <a
                        href={`https://www.google.com/maps/dir/?api=1&destination=${latitude},${longitude}`}
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        <Button className="w-full">
                          <Navigation className="w-4 h-4 mr-2" />
                          Get Directions
                        </Button>
                      </a>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Opening Hours */}
              {openingHoursArray && openingHoursArray.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle>Opening Hours</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {openingHoursArray.map((hours, index) => (
                        <div key={index} className="flex justify-between">
                          <span className="text-muted-foreground">{hours}</span>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Reviews Section */}
              {reviews && reviews.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle>Reviews ({reviews.length})</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    {reviews.slice(0, 5).map((review) => (
                      <div key={review.id} className="space-y-2">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
                              <User className="w-4 h-4" />
                            </div>
                            <div>
                              <p className="font-medium">
                                {review.author?.name || "Anonymous"}
                              </p>
                              <div className="flex items-center">
                                {[...Array(5)].map((_, i) => (
                                  <Star
                                    key={i}
                                    className={`w-4 h-4 ${
                                      i < review.rating
                                        ? "fill-yellow-400 text-yellow-400"
                                        : "text-gray-300"
                                    }`}
                                  />
                                ))}
                              </div>
                            </div>
                          </div>
                          <span className="text-sm text-muted-foreground">
                            {new Date(review.createdAt).toLocaleDateString()}
                          </span>
                        </div>
                        {review.title && (
                          <h4 className="font-medium">{review.title}</h4>
                        )}
                        <p className="text-muted-foreground">
                          {review.content}
                        </p>
                        {review.visitDate && (
                          <p className="text-sm text-muted-foreground">
                            Visited:{" "}
                            {new Date(review.visitDate).toLocaleDateString()}
                          </p>
                        )}
                        {review !== reviews[reviews.length - 1] && (
                          <Separator />
                        )}
                      </div>
                    ))}
                    {reviews.length > 5 && (
                      <div className="text-center">
                        <Button variant="outline">
                          View All Reviews ({reviews.length})
                        </Button>
                      </div>
                    )}
                  </CardContent>
                </Card>
              )}
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Quick Info */}
              <Card>
                <CardHeader>
                  <CardTitle>Quick Info</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Halal</span>
                    <span className="font-medium">✅ Yes</span>
                  </div>
                  {halalCertification && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">
                        Certification
                      </span>
                      <span className="font-medium">{halalCertification}</span>
                    </div>
                  )}
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Verified</span>
                    <span className="font-medium">
                      {isVerified ? "✅ Yes" : "⏳ Pending"}
                    </span>
                  </div>
                  {priceLevel && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Price Level</span>
                      <span className="font-medium">
                        {"$".repeat(priceLevel)}
                      </span>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Added By */}
              {addedBy && (
                <Card>
                  <CardHeader>
                    <CardTitle>Added By</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center space-x-2">
                      <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
                        <User className="w-4 h-4" />
                      </div>
                      <div>
                        <p className="font-medium">{addedBy.name}</p>
                        <p className="text-sm text-muted-foreground">
                          {new Date(createdAt).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Actions */}
              <Card>
                <CardHeader>
                  <CardTitle>Actions</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <Button variant="outline" className="w-full">
                    <Star className="w-4 h-4 mr-2" />
                    Write Review
                  </Button>
                  <Button variant="outline" className="w-full">
                    <Camera className="w-4 h-4 mr-2" />
                    Add Photos
                  </Button>
                  {googlePlaceId && (
                    <a
                      href={`https://www.google.com/maps/place/?q=place_id:${googlePlaceId}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="block"
                    >
                      <Button variant="outline" className="w-full">
                        <ExternalLink className="w-4 h-4 mr-2" />
                        View on Google Maps
                      </Button>
                    </a>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
