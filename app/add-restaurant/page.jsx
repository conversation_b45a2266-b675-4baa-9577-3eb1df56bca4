import GooglePlacesSearch from "../components/GooglePlacesSearch";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "../../components/ui/card";
import { ArrowLeft, Search } from "lucide-react";
import Link from "next/link";
import { But<PERSON> } from "../../components/ui/button";

export default function AddRestaurantPage() {
  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b bg-white/95 backdrop-blur supports-[backdrop-filter]:bg-white/60">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center space-x-4">
            <Link href="/">
              <Button variant="ghost" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Search
              </Button>
            </Link>
            <div>
              <h1 className="text-2xl font-bold text-primary">
                Add New Restaurant
              </h1>
              <p className="text-muted-foreground">
                Search and add halal restaurants to our directory
              </p>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {/* Search Instructions */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Search className="h-5 w-5 mr-2" />
                Search for Restaurant
              </CardTitle>
              <p className="text-muted-foreground">
                Search for a halal restaurant using Google Places. Once you find
                it, simply click "Add to Directory" to include it in our
                database.
              </p>
            </CardHeader>
            <CardContent>
              <GooglePlacesSearch />
            </CardContent>
          </Card>

          {/* How it Works */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="text-lg">How it Works</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center p-4 border rounded-lg">
                  <div className="text-2xl mb-2">🔍</div>
                  <h3 className="font-semibold mb-1">1. Search</h3>
                  <p className="text-sm text-muted-foreground">
                    Type the restaurant name and location to find it on Google
                    Places
                  </p>
                </div>
                <div className="text-center p-4 border rounded-lg">
                  <div className="text-2xl mb-2">✅</div>
                  <h3 className="font-semibold mb-1">2. Verify</h3>
                  <p className="text-sm text-muted-foreground">
                    Check that it's a halal restaurant and confirm the details
                  </p>
                </div>
                <div className="text-center p-4 border rounded-lg">
                  <div className="text-2xl mb-2">➕</div>
                  <h3 className="font-semibold mb-1">3. Add</h3>
                  <p className="text-sm text-muted-foreground">
                    Click "Add to Directory" to include it in our halal
                    restaurant database
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Guidelines */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Submission Guidelines</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3 text-sm text-muted-foreground">
              <div className="flex items-start space-x-2">
                <span className="text-green-600 font-bold">✓</span>
                <span>Only add restaurants that serve halal food</span>
              </div>
              <div className="flex items-start space-x-2">
                <span className="text-green-600 font-bold">✓</span>
                <span>Verify the restaurant information is accurate</span>
              </div>
              <div className="flex items-start space-x-2">
                <span className="text-green-600 font-bold">✓</span>
                <span>Check if the restaurant is currently open</span>
              </div>
              <div className="flex items-start space-x-2">
                <span className="text-red-600 font-bold">✗</span>
                <span>
                  Don't add duplicate restaurants (we'll check automatically)
                </span>
              </div>
              <div className="flex items-start space-x-2">
                <span className="text-red-600 font-bold">✗</span>
                <span>Don't add restaurants that are permanently closed</span>
              </div>
              <div className="flex items-start space-x-2">
                <span className="text-red-600 font-bold">✗</span>
                <span>Don't add restaurants that don't serve halal food</span>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
}
