/**
 * RESTAURANTS LISTING PAGE
 *
 * This page displays all restaurants with advanced filtering and pagination.
 * It provides a comprehensive view of the restaurant directory with search,
 * filter, and sort capabilities.
 *
 * Key Features:
 * - Paginated restaurant listing
 * - Advanced filtering (cuisine, price, verification status, location)
 * - Search functionality
 * - Sort options (name, rating, distance, date added)
 * - Responsive grid layout
 * - Filter persistence via URL parameters
 *
 * @see app/components/RestaurantFilters.jsx for filter components
 * @see app/components/RestaurantGrid.jsx for restaurant display
 * @see app/actions/restaurant-actions.js for data fetching
 */

import { Suspense } from "react";
import { Card, CardContent } from "../../components/ui/card";
import { Button } from "../../components/ui/button";
import { Skeleton } from "../../components/ui/skeleton";
import RestaurantFilters from "../components/RestaurantFilters";
import RestaurantGrid from "../components/RestaurantGrid";
import RestaurantPagination from "../components/RestaurantPagination";
import { PageHeader } from "../components/Navigation";
import { getRestaurantsWithFilters } from "../actions/restaurant-actions";
import { Search, Filter, MapPin, Star, Plus } from "lucide-react";
import Link from "next/link";

/**
 * RESTAURANTS PAGE COMPONENT
 *
 * Server Component that handles initial data fetching and renders
 * the restaurant listing with filters and pagination.
 */
export default async function RestaurantsPage({ searchParams }) {
  // Await searchParams as required by Next.js 15
  const params = await searchParams;

  // Extract search parameters with defaults
  const page = parseInt(params.page) || 1;
  const limit = parseInt(params.limit) || 12;
  const search = params.search || "";
  const cuisine = params.cuisine || "";
  const priceLevel = params.priceLevel ? parseInt(params.priceLevel) : null;
  const verified =
    params.verified === "true"
      ? true
      : params.verified === "false"
      ? false
      : null;
  const city = params.city || "";
  const sortBy = params.sortBy || "name";
  const sortOrder = params.sortOrder || "asc";

  // Build filters object
  const filters = {
    search,
    cuisine,
    priceLevel,
    verified,
    city,
    sortBy,
    sortOrder,
    page,
    limit,
  };

  // Fetch restaurants with filters
  const result = await getRestaurantsWithFilters(filters);
  const { restaurants, total, totalPages, currentPage } = result;

  return (
    <div className="min-h-screen bg-background">
      {/* Page Header */}
      <PageHeader
        title="Halal Restaurants"
        description={`Discover ${total} verified halal restaurants in your area`}
        breadcrumb={[{ name: "Home", href: "/" }, { name: "All Restaurants" }]}
      >
        {/* Quick Stats */}
        <div className="flex gap-4 text-sm text-muted-foreground">
          <div className="flex items-center gap-1">
            <MapPin className="h-4 w-4" />
            <span>{restaurants.filter((r) => r.verified).length} Verified</span>
          </div>
          <div className="flex items-center gap-1">
            <Star className="h-4 w-4" />
            <span>
              {restaurants.length > 0
                ? (
                    restaurants.reduce((acc, r) => acc + (r.rating || 0), 0) /
                    restaurants.length
                  ).toFixed(1)
                : "0"}{" "}
              Avg Rating
            </span>
          </div>
        </div>

        {/* Add Restaurant Button */}
        <Button asChild>
          <Link href="/add-restaurant">
            <Plus className="h-4 w-4 mr-2" />
            Add Restaurant
          </Link>
        </Button>
      </PageHeader>

      <div className="container mx-auto px-4 py-6">
        <div className="flex flex-col lg:flex-row gap-6">
          {/* Filters Sidebar */}
          <aside className="lg:w-80 flex-shrink-0">
            <div className="sticky top-6">
              <Suspense fallback={<FiltersSkeleton />}>
                <RestaurantFilters
                  initialFilters={filters}
                  totalResults={total}
                />
              </Suspense>
            </div>
          </aside>

          {/* Main Content */}
          <main className="flex-1 min-w-0">
            {/* Results Header */}
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Search className="h-4 w-4" />
                <span>
                  Showing {(currentPage - 1) * limit + 1}-
                  {Math.min(currentPage * limit, total)} of {total} restaurants
                </span>
              </div>

              {/* View Toggle - Future enhancement */}
              <div className="flex items-center gap-2">
                <span className="text-sm text-muted-foreground">View:</span>
                <Button
                  variant="outline"
                  size="sm"
                  className="bg-primary text-primary-foreground"
                >
                  Grid
                </Button>
                <Button variant="outline" size="sm" disabled>
                  List
                </Button>
              </div>
            </div>

            {/* Restaurant Grid */}
            <Suspense fallback={<RestaurantGridSkeleton />}>
              <RestaurantGrid restaurants={restaurants} />
            </Suspense>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="mt-8">
                <RestaurantPagination
                  currentPage={currentPage}
                  totalPages={totalPages}
                  total={total}
                  limit={limit}
                />
              </div>
            )}

            {/* Empty State */}
            {restaurants.length === 0 && (
              <Card className="text-center py-12">
                <CardContent>
                  <Search className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                  <h3 className="text-lg font-semibold mb-2">
                    No restaurants found
                  </h3>
                  <p className="text-muted-foreground mb-4">
                    Try adjusting your filters or search terms
                  </p>
                  <Button
                    variant="outline"
                    onClick={() => (window.location.href = "/restaurants")}
                  >
                    Clear All Filters
                  </Button>
                </CardContent>
              </Card>
            )}
          </main>
        </div>
      </div>
    </div>
  );
}

/**
 * LOADING SKELETONS
 */
function FiltersSkeleton() {
  return (
    <div className="space-y-6">
      <Skeleton className="h-8 w-32" />
      <div className="space-y-4">
        {[1, 2, 3, 4].map((i) => (
          <div key={i} className="space-y-2">
            <Skeleton className="h-4 w-24" />
            <Skeleton className="h-10 w-full" />
          </div>
        ))}
      </div>
    </div>
  );
}

function RestaurantGridSkeleton() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
      {[1, 2, 3, 4, 5, 6].map((i) => (
        <Card key={i} className="overflow-hidden">
          <Skeleton className="h-48 w-full" />
          <CardContent className="p-4 space-y-2">
            <Skeleton className="h-6 w-3/4" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-1/2" />
          </CardContent>
        </Card>
      ))}
    </div>
  );
}

/**
 * METADATA
 */
export const metadata = {
  title: "Halal Restaurants Directory | Find Verified Halal Food",
  description:
    "Browse our comprehensive directory of verified halal restaurants. Filter by cuisine, location, price range, and more.",
  keywords:
    "halal restaurants, halal food, muslim dining, verified halal, restaurant directory",
};
