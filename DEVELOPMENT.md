# 🛠️ Development Guide

This document provides detailed information for developers working on the Halal Restaurant Directory project.

## 📋 Table of Contents

1. [Development Setup](#development-setup)
2. [Code Organization](#code-organization)
3. [Key Components](#key-components)
4. [Database Operations](#database-operations)
5. [API Integration](#api-integration)
6. [Testing Strategy](#testing-strategy)
7. [Performance Considerations](#performance-considerations)
8. [Debugging Guide](#debugging-guide)

## 🚀 Development Setup

### Local Environment

1. **Prerequisites**
   ```bash
   # Check Node.js version (18+ required)
   node --version
   
   # Check npm version
   npm --version
   
   # Install PostgreSQL (if using local database)
   # macOS: brew install postgresql
   # Ubuntu: sudo apt-get install postgresql
   ```

2. **Environment Variables**
   Create `.env.local` with the following variables:
   ```env
   # Database
   DATABASE_URL="postgresql://username:password@localhost:5432/halal_directory"
   
   # Google APIs
   GOOGLE_PLACES_API_KEY="your_server_side_api_key"
   NEXT_PUBLIC_GOOGLE_MAPS_API_KEY="your_client_side_api_key"
   
   # Development
   NODE_ENV="development"
   ```

3. **Database Setup**
   ```bash
   # Generate Prisma client
   npx prisma generate
   
   # Push schema to database
   npx prisma db push
   
   # Seed with test data
   node scripts/seed.js
   
   # Open Prisma Studio (optional)
   npx prisma studio
   ```

### Development Workflow

1. **Start Development Server**
   ```bash
   npm run dev
   ```

2. **Database Management**
   ```bash
   # View current schema
   npx prisma db pull
   
   # Reset database (careful!)
   npx prisma db push --force-reset
   
   # View data
   npx prisma studio
   ```

3. **Useful Scripts**
   ```bash
   # Get restaurant IDs for testing
   node scripts/get-restaurant-ids.js
   
   # Test Google Places API
   curl http://localhost:3001/api/test-google-places
   ```

## 📁 Code Organization

### Directory Structure Explained

```
app/
├── actions/                    # Server Actions (form handling, mutations)
│   └── restaurant-actions.js   # Restaurant CRUD operations
├── api/                       # API Routes (external integrations)
│   ├── photo/[photoReference]/ # Google Photos proxy
│   └── test-google-places/    # API health check
├── components/                # React Components
│   ├── GooglePlacesSearch.jsx # Google Places integration
│   ├── RestaurantDetailsPage.jsx # Restaurant detail view
│   ├── RestaurantList.jsx     # Restaurant listing
│   └── SearchForm.jsx         # Search interface
├── lib/                       # Utility libraries
│   ├── db.js                 # Database service layer
│   └── googlePlaces.js       # Google Places API wrapper
├── restaurant/                # Dynamic routes
│   ├── [id]/                 # Local restaurant details
│   └── google/[placeId]/     # Google Places restaurant details
└── globals.css               # Global styles and Tailwind imports
```

### Component Hierarchy

```
App Layout (layout.js)
├── Home Page (page.js)
│   ├── SearchForm
│   └── RestaurantList
│       └── RestaurantCard (multiple)
├── Add Restaurant Page
│   └── GooglePlacesSearch
│       └── RestaurantSearchCard (multiple)
└── Restaurant Details Page
    ├── Photo Gallery
    ├── Restaurant Info
    ├── Contact Information
    ├── Reviews Section
    └── Action Buttons
```

## 🧩 Key Components

### 1. RestaurantList Component

**Purpose**: Displays search results from both local database and Google Places API

**Key Features**:
- Server Component for optimal performance
- Hybrid data handling (local + Google)
- Responsive card layout
- Image optimization with fallbacks

**Usage**:
```jsx
// In page.js
<RestaurantList query={query} location={location} />
```

**Data Flow**:
```
searchParams → RestaurantList → searchRestaurants() → Database + Google API → RestaurantCard[]
```

### 2. GooglePlacesSearch Component

**Purpose**: Interactive search and addition of restaurants from Google Places

**Key Features**:
- Client Component for interactivity
- Real-time search with debouncing
- State management for UI feedback
- Integration with Server Actions

**State Management**:
```javascript
const [query, setQuery] = useState("");           // Search query
const [searchResults, setSearchResults] = useState([]); // API results
const [isSearching, setIsSearching] = useState(false);  // Loading state
const [addingRestaurants, setAddingRestaurants] = useState(new Set()); // Adding state
```

### 3. RestaurantDetailsPage Component

**Purpose**: Comprehensive restaurant detail view

**Key Features**:
- Hybrid data display (local + Google)
- Photo gallery with navigation
- Contact information with actions
- Reviews display
- Responsive layout

**Data Sources**:
- Local database via `getRestaurantDetails(id, false)`
- Google Places via `getRestaurantDetails(placeId, true)`
- Combined display with source attribution

## 🗄️ Database Operations

### Service Layer Pattern

The application uses a service layer pattern to abstract database operations:

```javascript
// app/lib/db.js
export const restaurantService = {
  async create(data) { /* ... */ },
  async findById(id) { /* ... */ },
  async findByLocation(lat, lng, radius) { /* ... */ },
  async search(query, filters) { /* ... */ },
  // ... more methods
};
```

### Key Database Patterns

1. **Optimized Includes**
   ```javascript
   include: {
     photos: true,
     reviews: {
       include: {
         author: {
           select: { id: true, name: true, avatar: true }
         }
       }
     }
   }
   ```

2. **Geographic Queries**
   ```javascript
   // Radius-based search using lat/lng bounds
   const latDelta = radiusKm / 111;
   const lngDelta = radiusKm / (111 * Math.cos(lat * Math.PI / 180));
   ```

3. **Text Search**
   ```javascript
   where: {
     OR: [
       { name: { contains: query, mode: 'insensitive' } },
       { description: { contains: query, mode: 'insensitive' } },
       { address: { contains: query, mode: 'insensitive' } }
     ]
   }
   ```

## 🔌 API Integration

### Google Places API Wrapper

The `googlePlaces.js` module provides a clean interface for Google Places API:

```javascript
// Core functions
export async function searchHalalRestaurants(query, location)
export async function searchPlacesByText(query)
export async function getPlaceDetails(placeId)
```

### Caching Strategy

1. **Database Caching**
   - 30-day cache for Google Places data
   - Automatic expiry and cleanup
   - Compliance with Google ToS

2. **Implementation**
   ```javascript
   async function getCachedPlaceData(placeId) {
     const cached = await prisma.googlePlacesCache.findUnique({
       where: { placeId }
     });
     
     if (cached && new Date() <= cached.expiresAt) {
       return cached.googleData;
     }
     
     return null; // Cache miss or expired
   }
   ```

### Error Handling

```javascript
try {
  const response = await client.textSearch({ /* ... */ });
  return normalizeGooglePlacesResponse(response.data);
} catch (error) {
  console.error('Google Places API Error:', error);
  
  if (error.response?.status === 429) {
    // Rate limit exceeded
    throw new Error('Search temporarily unavailable');
  }
  
  throw new Error('Search failed');
}
```

## 🧪 Testing Strategy

### Manual Testing

1. **Restaurant Search**
   ```bash
   # Test local search
   curl "http://localhost:3001/?q=halal&location=New York"
   
   # Test Google Places integration
   # Use the add restaurant page
   ```

2. **Database Operations**
   ```bash
   # Check restaurant data
   node scripts/get-restaurant-ids.js
   
   # Verify database connection
   npx prisma studio
   ```

3. **API Integration**
   ```bash
   # Test Google Places API
   curl http://localhost:3001/api/test-google-places
   ```

### Automated Testing (Future)

Recommended testing setup:
- **Unit Tests**: Jest + React Testing Library
- **Integration Tests**: Playwright for E2E
- **API Tests**: Supertest for API routes
- **Database Tests**: Test database with Docker

## ⚡ Performance Considerations

### Database Optimization

1. **Indexes**
   ```sql
   -- Geographic queries
   CREATE INDEX idx_restaurant_location ON Restaurant(latitude, longitude);
   
   -- Text search
   CREATE INDEX idx_restaurant_name ON Restaurant(name);
   
   -- Filtering
   CREATE INDEX idx_restaurant_city ON Restaurant(city);
   ```

2. **Query Optimization**
   - Use selective includes to avoid over-fetching
   - Implement pagination for large result sets
   - Use database-level filtering instead of application filtering

### Frontend Optimization

1. **Image Optimization**
   ```jsx
   <Image
     src={photoUrl}
     alt={name}
     fill
     className="object-cover"
     sizes="(max-width: 768px) 100vw, 50vw"
     priority={isPrimary}
   />
   ```

2. **Code Splitting**
   - Automatic route-based splitting
   - Dynamic imports for heavy components
   - Lazy loading for non-critical features

### API Optimization

1. **Caching**
   - Database caching for Google Places data
   - Next.js built-in caching for API routes
   - Browser caching for static assets

2. **Rate Limiting**
   - Implement request debouncing
   - Batch API requests where possible
   - Monitor API usage and costs

## 🐛 Debugging Guide

### Common Issues

1. **Database Connection Issues**
   ```bash
   # Check database URL
   echo $DATABASE_URL
   
   # Test connection
   npx prisma db pull
   
   # Reset if needed
   npx prisma db push --force-reset
   ```

2. **Google Places API Issues**
   ```bash
   # Test API key
   curl "https://maps.googleapis.com/maps/api/place/textsearch/json?query=restaurant&key=YOUR_API_KEY"
   
   # Check quotas in Google Cloud Console
   ```

3. **Next.js Issues**
   ```bash
   # Clear Next.js cache
   rm -rf .next
   
   # Restart development server
   npm run dev
   ```

### Debugging Tools

1. **Database**
   - Prisma Studio for data visualization
   - PostgreSQL logs for query analysis
   - Database query profiling

2. **API**
   - Browser Network tab for API calls
   - Next.js built-in logging
   - Google Cloud Console for API monitoring

3. **Frontend**
   - React Developer Tools
   - Next.js built-in debugging
   - Browser Performance tab

### Logging

```javascript
// Development logging
if (process.env.NODE_ENV === 'development') {
  console.log('Debug info:', data);
}

// Production error logging
console.error('Error:', error.message, {
  stack: error.stack,
  context: additionalContext
});
```

---

For more detailed architecture information, see [ARCHITECTURE.md](./ARCHITECTURE.md).
