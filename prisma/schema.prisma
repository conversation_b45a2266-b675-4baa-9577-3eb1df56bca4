/**
 * HALAL RESTAURANT DIRECTORY - DATABASE SCHEMA
 * This Prisma schema defines the database structure for a halal restaurant directory
 * that integrates with Google Places API while maintaining user-contributed content.
 * Key Design Principles:
 * - Google Places API compliance (only Place IDs stored permanently)
 * - User-generated content focus (reviews, photos, restaurant additions)
 * - Scalable architecture with proper indexing
 * - Data integrity with foreign key constraints
 * @see https://www.prisma.io/docs/concepts/components/prisma-schema
 */

// Prisma client configuration
generator client {
  provider = "prisma-client-js"
  output   = "../app/generated/prisma" // Custom output path for Next.js app structure
}

// Database connection configuration
datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL") // Connection string from environment variables
}

/**
 * RESTAURANT MODEL
 * Core entity representing halal restaurants in the directory.
 * Designed to store user-contributed data while maintaining Google Places API compliance.
 * Key Features:
 * - User-contributed restaurant information
 * - Optional Google Places integration via Place ID
 * - Location-based search optimization
 * - Halal certification tracking
 * - Manual verification system
 */
model Restaurant {
  id String @id @default(cuid()) // Unique identifier using CUID for better performance

  // === USER-CONTRIBUTED CORE DATA ===
  // This data can be stored permanently as it's user-provided, not from Google
  name        String // Restaurant name (required)
  description String? // User-provided description
  address     String // Full address (required for location)
  phone       String? // Contact phone number
  website     String? // Restaurant website URL
  email       String? // Contact email address

  // === LOCATION DATA ===
  // Geographic coordinates and address components for search and mapping
  latitude  Float // GPS latitude coordinate (required for location-based search)
  longitude Float // GPS longitude coordinate (required for location-based search)
  city      String // City name (required for filtering and search)
  state     String? // State/province (optional, varies by country)
  country   String  @default("US") // Country code (defaults to US)
  zipCode   String? // Postal/ZIP code (optional)

  // === RESTAURANT CHARACTERISTICS ===
  // Business-specific information for filtering and categorization
  cuisine            String[] // Array of cuisine types (e.g., ["Pakistani", "Indian"])
  priceLevel         Int? // Price level 1-4 scale ($ to $$$$, user assessment)
  isHalal            Boolean  @default(true) // Halal status (defaults to true for this directory)
  halalCertification String? // Certification body name (e.g., "ISWA", "HMA", "ICNA")
  isVerified         Boolean  @default(false) // Manual verification status by admins

  // === BUSINESS HOURS ===
  // Operating hours stored as JSON for flexibility
  openingHours Json? // Flexible JSON structure for complex hour schedules

  // === GOOGLE PLACES INTEGRATION ===
  // Only Place ID is stored permanently (compliant with Google Places API ToS)
  googlePlaceId String? @unique // Google Place ID for API correlation (if available)

  // === RELATIONSHIPS ===
  // Connected entities for rich restaurant profiles
  photos    RestaurantPhoto[] // User-uploaded photos (Google photos served via proxy)
  reviews   Review[] // User reviews and ratings
  addedBy   User?             @relation("AddedRestaurants", fields: [addedById], references: [id])
  addedById String? // User who added this restaurant (optional for anonymous additions)

  // === AUDIT TIMESTAMPS ===
  createdAt DateTime @default(now()) // Record creation timestamp
  updatedAt DateTime @updatedAt // Last modification timestamp

  // === DATABASE INDEXES ===
  // Optimized for common query patterns
  @@index([latitude, longitude]) // Geospatial queries for location-based search
  @@index([city]) // City-based filtering
  @@index([isHalal]) // Halal status filtering
  @@index([isVerified]) // Verification status filtering
  @@index([googlePlaceId]) // Google Places correlation
}

/**
 * GOOGLE PLACES CACHE MODEL
 * Temporary storage for Google Places API data to optimize performance and reduce API costs.
 * Complies with Google Places API Terms of Service (30-day cache limit).
 * Key Features:
 * - Temporary caching of Google Places API responses
 * - Automatic expiry management
 * - Performance optimization for repeated requests
 * - Cost reduction for API usage
 */
model GooglePlacesCache {
  id      String @id @default(cuid()) // Unique cache entry identifier
  placeId String @unique // Google Place ID (unique constraint for cache lookup)

  // === CACHED DATA ===
  // Complete Google Places API response stored as JSON
  googleData Json // Full API response data (photos, reviews, details, etc.)

  // === CACHE MANAGEMENT ===
  // Automatic expiry system for ToS compliance
  cachedAt  DateTime @default(now()) // When the data was cached
  expiresAt DateTime // Expiry date (set to 30 days from cachedAt)

  // === DATABASE INDEXES ===
  @@index([placeId]) // Fast lookup by Place ID
  @@index([expiresAt]) // Efficient cleanup of expired entries
}

/**
 * RESTAURANT PHOTO MODEL
 * User-uploaded photos for restaurants. Google Photos are served via proxy API
 * and not stored in the database to comply with Google Places API Terms of Service.
 * Key Features:
 * - User-generated content only
 * - Primary photo designation for featured images
 * - Metadata storage for image optimization
 * - Cascade deletion with restaurant removal
 */
model RestaurantPhoto {
  id        String  @id @default(cuid()) // Unique photo identifier
  url       String // Photo URL (user-uploaded photos only, not Google Photos)
  caption   String? // Optional user-provided caption
  isPrimary Boolean @default(false) // Designates the main/featured photo for the restaurant
  width     Int? // Image width in pixels (for optimization)
  height    Int? // Image height in pixels (for optimization)

  // === RELATIONSHIPS ===
  restaurant   Restaurant @relation(fields: [restaurantId], references: [id], onDelete: Cascade)
  restaurantId String // Foreign key to Restaurant (cascade delete when restaurant is removed)

  uploadedBy   User   @relation(fields: [uploadedById], references: [id])
  uploadedById String // User who uploaded this photo (required for attribution)

  // === AUDIT TIMESTAMPS ===
  createdAt DateTime @default(now()) // Photo upload timestamp

  // === DATABASE INDEXES ===
  @@index([restaurantId]) // Fast lookup of photos by restaurant
  @@index([isPrimary]) // Quick filtering for primary photos
}

/**
 * USER MODEL
 * User accounts for the halal restaurant directory. Supports user-generated content
 * including restaurant additions, reviews, and photo uploads.
 * Key Features:
 * - User authentication and profile management
 * - Personalized preferences for better recommendations
 * - Content attribution and moderation
 * - Activity tracking and user contributions
 */
model User {
  id     String  @id @default(cuid()) // Unique user identifier
  email  String  @unique // Email address (unique, used for authentication)
  name   String // Display name
  avatar String? // Profile picture URL (optional)

  // === USER PREFERENCES ===
  // Personalization data for improved user experience
  preferredCuisines String[] // Array of preferred cuisine types for recommendations
  location          String? // User's location for location-based suggestions

  // === RELATIONSHIPS ===
  // User-generated content and contributions
  reviews          Review[] // Reviews written by this user
  addedRestaurants Restaurant[]      @relation("AddedRestaurants") // Restaurants added by this user
  photos           RestaurantPhoto[] // Photos uploaded by this user

  // === AUDIT TIMESTAMPS ===
  createdAt DateTime @default(now()) // Account creation timestamp
  updatedAt DateTime @updatedAt // Last profile update timestamp

  // === DATABASE INDEXES ===
  @@index([email]) // Fast lookup for authentication
}

/**
 * REVIEW MODEL
 * User reviews and ratings for restaurants. Supports rich review content with
 * moderation capabilities and prevents duplicate reviews per user per restaurant.
 * Key Features:
 * - 1-5 star rating system
 * - Rich text reviews with optional titles
 * - Visit date tracking for authenticity
 * - Moderation system for quality control
 * - Unique constraint prevents review spam
 */
model Review {
  id      String  @id @default(cuid()) // Unique review identifier
  rating  Int // Rating on 1-5 scale (required)
  title   String? // Optional review title/summary
  content String // Review text content (required)

  // === REVIEW METADATA ===
  // Additional context and authenticity indicators
  visitDate     DateTime? // When the user visited the restaurant (optional)
  isRecommended Boolean? // Whether the user recommends this restaurant (optional)

  // === RELATIONSHIPS ===
  restaurant   Restaurant @relation(fields: [restaurantId], references: [id], onDelete: Cascade)
  restaurantId String // Foreign key to Restaurant (cascade delete when restaurant is removed)

  author   User   @relation(fields: [authorId], references: [id], onDelete: Cascade)
  authorId String // Foreign key to User (cascade delete when user account is removed)

  // === MODERATION SYSTEM ===
  // Content quality and spam prevention
  isApproved  Boolean   @default(true) // Review approval status (defaults to approved)
  moderatedAt DateTime? // Timestamp of moderation action (if any)

  // === AUDIT TIMESTAMPS ===
  createdAt DateTime @default(now()) // Review creation timestamp
  updatedAt DateTime @updatedAt // Last review update timestamp

  // === DATABASE CONSTRAINTS & INDEXES ===
  @@unique([restaurantId, authorId]) // Prevents multiple reviews from same user for same restaurant
  @@index([restaurantId]) // Fast lookup of reviews by restaurant
  @@index([authorId]) // Fast lookup of reviews by user
  @@index([rating]) // Filtering and sorting by rating
  @@index([createdAt]) // Chronological sorting of reviews
}
