// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  output   = "../app/generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Restaurant {
  id String @id @default(cuid())

  // User-contributed data (can be stored permanently)
  name        String
  description String?
  address     String
  phone       String?
  website     String?
  email       String?

  // Location data (user-provided, not from Google)
  latitude  Float
  longitude Float
  city      String
  state     String?
  country   String  @default("US")
  zipCode   String?

  // Restaurant details (user-contributed)
  cuisine            String[]
  priceLevel         Int? // 1-4 scale (user assessment)
  isHalal            Boolean  @default(true)
  halalCertification String? // Certification body
  isVerified         Boolean  @default(false)

  // Operating hours (user-provided)
  openingHours Json?

  // Google Places reference (ONLY store Place ID permanently)
  googlePlaceId String? @unique // This is allowed to be stored permanently

  // Media (user-uploaded only)
  photos RestaurantPhoto[]

  // Relationships
  reviews   Review[]
  addedBy   User?    @relation("AddedRestaurants", fields: [addedById], references: [id])
  addedById String?

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([latitude, longitude])
  @@index([city])
  @@index([isHalal])
  @@index([isVerified])
  @@index([googlePlaceId])
}

// Temporary cache for Google Places data (30-day limit)
model GooglePlacesCache {
  id      String @id @default(cuid())
  placeId String @unique

  // Cached Google data (will be purged after 30 days)
  googleData Json // Store all Google Places API response data

  // Cache management
  cachedAt  DateTime @default(now())
  expiresAt DateTime // Set to 30 days from cachedAt

  @@index([placeId])
  @@index([expiresAt])
}

model RestaurantPhoto {
  id        String  @id @default(cuid())
  url       String // User-uploaded photos only
  caption   String?
  isPrimary Boolean @default(false)
  width     Int?
  height    Int?

  restaurant   Restaurant @relation(fields: [restaurantId], references: [id], onDelete: Cascade)
  restaurantId String

  uploadedBy   User   @relation(fields: [uploadedById], references: [id])
  uploadedById String

  createdAt DateTime @default(now())

  @@index([restaurantId])
  @@index([isPrimary])
}

model User {
  id     String  @id @default(cuid())
  email  String  @unique
  name   String
  avatar String?

  // User preferences
  preferredCuisines String[]
  location          String?

  // Relationships
  reviews          Review[]
  addedRestaurants Restaurant[]      @relation("AddedRestaurants")
  photos           RestaurantPhoto[]

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([email])
}

model Review {
  id      String  @id @default(cuid())
  rating  Int // 1-5 scale
  title   String?
  content String

  // Review details
  visitDate     DateTime?
  isRecommended Boolean?

  // Relationships
  restaurant   Restaurant @relation(fields: [restaurantId], references: [id], onDelete: Cascade)
  restaurantId String

  author   User   @relation(fields: [authorId], references: [id], onDelete: Cascade)
  authorId String

  // Moderation
  isApproved  Boolean   @default(true)
  moderatedAt DateTime?

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([restaurantId, authorId]) // One review per user per restaurant
  @@index([restaurantId])
  @@index([authorId])
  @@index([rating])
  @@index([createdAt])
}
