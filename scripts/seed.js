const { PrismaClient } = require("../app/generated/prisma/index.js");

const prisma = new PrismaClient();

async function main() {
  console.log("🌱 Starting database seeding...");

  // Create a test user first
  const testUser = await prisma.user.upsert({
    where: { email: "<EMAIL>" },
    update: {},
    create: {
      email: "<EMAIL>",
      name: "Test User",
      preferredCuisines: ["Middle Eastern", "Pakistani"],
      location: "New York, NY",
    },
  });

  console.log("✅ Created test user:", testUser.name);

  // Create sample restaurants
  const restaurants = [
    {
      name: "Halal Guys",
      description:
        "Famous halal food cart serving chicken and rice with white and hot sauce",
      address: "307 E 14th St, New York, NY 10003",
      phone: "+****************",
      website: "https://thehalalguys.com",
      latitude: 40.7328,
      longitude: -73.9877,
      city: "New York",
      state: "NY",
      country: "US",
      zipCode: "10003",
      cuisine: ["Middle Eastern", "American"],
      priceLevel: 2,
      isHalal: true,
      halalCertification: "ISWA",
      isVerified: true,
      openingHours: JSON.stringify([
        "Monday: 11:00 AM - 12:00 AM",
        "Tuesday: 11:00 AM - 12:00 AM",
        "Wednesday: 11:00 AM - 12:00 AM",
        "Thursday: 11:00 AM - 12:00 AM",
        "Friday: 11:00 AM - 3:00 AM",
        "Saturday: 11:00 AM - 3:00 AM",
        "Sunday: 11:00 AM - 12:00 AM",
      ]),
      addedById: testUser.id,
    },
    {
      name: "Masjid Al-Farooq Restaurant",
      description:
        "Authentic Pakistani and Indian halal cuisine in the heart of Brooklyn",
      address: "1926 Coney Island Ave, Brooklyn, NY 11223",
      phone: "+****************",
      latitude: 40.6089,
      longitude: -73.9628,
      city: "Brooklyn",
      state: "NY",
      country: "US",
      zipCode: "11223",
      cuisine: ["Pakistani", "Indian", "South Asian"],
      priceLevel: 2,
      isHalal: true,
      halalCertification: "ICNA",
      isVerified: true,
      openingHours: JSON.stringify([
        "Monday: 12:00 PM - 11:00 PM",
        "Tuesday: 12:00 PM - 11:00 PM",
        "Wednesday: 12:00 PM - 11:00 PM",
        "Thursday: 12:00 PM - 11:00 PM",
        "Friday: 12:00 PM - 11:30 PM",
        "Saturday: 12:00 PM - 11:30 PM",
        "Sunday: 12:00 PM - 11:00 PM",
      ]),
      addedById: testUser.id,
    },
    {
      name: "Naya Express",
      description:
        "Fast-casual Lebanese restaurant with fresh, healthy halal options",
      address: "688 6th Ave, New York, NY 10010",
      phone: "+****************",
      website: "https://nayarestaurants.com",
      email: "<EMAIL>",
      latitude: 40.7505,
      longitude: -73.9934,
      city: "New York",
      state: "NY",
      country: "US",
      zipCode: "10010",
      cuisine: ["Lebanese", "Mediterranean", "Middle Eastern"],
      priceLevel: 3,
      isHalal: true,
      halalCertification: "HMA",
      isVerified: true,
      openingHours: JSON.stringify([
        "Monday: 11:00 AM - 10:00 PM",
        "Tuesday: 11:00 AM - 10:00 PM",
        "Wednesday: 11:00 AM - 10:00 PM",
        "Thursday: 11:00 AM - 10:00 PM",
        "Friday: 11:00 AM - 10:00 PM",
        "Saturday: 11:00 AM - 10:00 PM",
        "Sunday: 11:00 AM - 9:00 PM",
      ]),
      addedById: testUser.id,
    },
  ];

  for (const restaurantData of restaurants) {
    // Check if restaurant already exists by name
    const existingRestaurant = await prisma.restaurant.findFirst({
      where: { name: restaurantData.name },
    });

    let restaurant;
    if (existingRestaurant) {
      console.log("⏭️  Restaurant already exists:", restaurantData.name);
      restaurant = existingRestaurant;
    } else {
      restaurant = await prisma.restaurant.create({
        data: restaurantData,
      });
      console.log("✅ Created restaurant:", restaurant.name);
    }

    // Add some sample reviews
    const reviews = [
      {
        rating: 5,
        title: "Amazing food!",
        content:
          "The food here is absolutely delicious. Fresh ingredients and great service. Highly recommend!",
        visitDate: new Date("2024-01-15"),
        isRecommended: true,
        restaurantId: restaurant.id,
        authorId: testUser.id,
      },
      {
        rating: 4,
        title: "Good value for money",
        content:
          "Great portions and reasonable prices. The atmosphere is nice and the staff is friendly.",
        visitDate: new Date("2024-02-10"),
        isRecommended: true,
        restaurantId: restaurant.id,
        authorId: testUser.id,
      },
    ];

    for (const reviewData of reviews) {
      try {
        const review = await prisma.review.create({
          data: reviewData,
        });
        console.log(`  ✅ Added review for ${restaurant.name}`);
      } catch (error) {
        // Skip if review already exists (unique constraint)
        if (error.code !== "P2002") {
          console.error(
            `  ❌ Error adding review for ${restaurant.name}:`,
            error.message
          );
        }
      }
    }
  }

  console.log("🎉 Database seeding completed!");
}

main()
  .catch((e) => {
    console.error("❌ Error during seeding:", e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
