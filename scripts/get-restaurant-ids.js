const { PrismaClient } = require("../app/generated/prisma/index.js");

const prisma = new PrismaClient();

async function main() {
  console.log('📋 Getting restaurant IDs...');

  const restaurants = await prisma.restaurant.findMany({
    select: {
      id: true,
      name: true,
      googlePlaceId: true,
    },
  });

  console.log('\n🏪 Restaurants in database:');
  restaurants.forEach((restaurant, index) => {
    console.log(`${index + 1}. ${restaurant.name}`);
    console.log(`   Local ID: ${restaurant.id}`);
    console.log(`   Google Place ID: ${restaurant.googlePlaceId || 'None'}`);
    console.log(`   Local URL: http://localhost:3001/restaurant/${restaurant.id}`);
    if (restaurant.googlePlaceId) {
      console.log(`   Google URL: http://localhost:3001/restaurant/google/${restaurant.googlePlaceId}`);
    }
    console.log('');
  });
}

main()
  .catch((e) => {
    console.error('❌ Error:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
