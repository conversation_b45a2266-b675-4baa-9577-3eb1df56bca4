# Halal Restaurant Directory - Architecture Documentation

## 🏗️ System Overview

The Halal Restaurant Directory is a Next.js 15 application that helps users discover and add halal restaurants. It integrates with Google Places API for restaurant discovery and maintains a local database for user-contributed content.

## 📋 Table of Contents

1. [Technology Stack](#technology-stack)
2. [Project Structure](#project-structure)
3. [Database Architecture](#database-architecture)
4. [API Integration](#api-integration)
5. [Routing Strategy](#routing-strategy)
6. [Component Architecture](#component-architecture)
7. [Data Flow](#data-flow)
8. [Security Considerations](#security-considerations)
9. [Performance Optimizations](#performance-optimizations)
10. [Deployment Strategy](#deployment-strategy)

## 🛠️ Technology Stack

### Frontend
- **Next.js 15**: React framework with App Router
- **React 19**: UI library with latest features
- **TypeScript**: Type safety (configured for JSX)
- **Tailwind CSS 4**: Utility-first CSS framework
- **shadcn/ui**: Component library built on Radix UI
- **Lucide React**: Icon library

### Backend
- **Next.js API Routes**: Server-side API endpoints
- **Prisma**: Database ORM and migration tool
- **PostgreSQL**: Primary database
- **Server Actions**: Next.js server-side form handling

### External APIs
- **Google Places API**: Restaurant discovery and details
- **Google Maps API**: Photo proxy and directions

### Development Tools
- **Prisma Studio**: Database management
- **ESLint**: Code linting
- **Prettier**: Code formatting (via IDE)

## 📁 Project Structure

```
directory-google/
├── app/                          # Next.js App Router directory
│   ├── actions/                  # Server Actions
│   │   └── restaurant-actions.js # Restaurant CRUD operations
│   ├── api/                      # API Routes
│   │   ├── photo/[photoReference]/ # Google Photos proxy
│   │   └── test-google-places/   # API testing endpoint
│   ├── components/               # React Components
│   │   ├── GooglePlacesSearch.jsx # Google Places integration
│   │   ├── RestaurantDetailsPage.jsx # Restaurant detail view
│   │   ├── RestaurantList.jsx    # Restaurant listing
│   │   └── SearchForm.jsx        # Search interface
│   ├── generated/                # Prisma generated client
│   │   └── prisma/              # Auto-generated Prisma client
│   ├── lib/                      # Utility libraries
│   │   ├── db.js                # Database service layer
│   │   └── googlePlaces.js      # Google Places API wrapper
│   ├── restaurant/               # Restaurant detail routes
│   │   ├── [id]/                # Local restaurant details
│   │   └── google/[placeId]/    # Google Places restaurant details
│   ├── add-restaurant/           # Restaurant addition page
│   ├── globals.css              # Global styles
│   ├── layout.js                # Root layout
│   └── page.js                  # Home page
├── components/                   # Shared UI components
│   └── ui/                      # shadcn/ui components
├── prisma/                      # Database schema and migrations
│   └── schema.prisma           # Database schema definition
├── scripts/                     # Utility scripts
│   ├── seed.js                 # Database seeding
│   └── get-restaurant-ids.js   # Development helper
└── package.json                # Dependencies and scripts
```

## 🗄️ Database Architecture

### Entity Relationship Diagram

```
User (1) ──────── (N) Restaurant
  │                      │
  │                      │
  └── (N) Review (N) ────┘
  │
  └── (N) RestaurantPhoto

GooglePlacesCache (Independent caching layer)
```

### Core Entities

#### Restaurant
- **Primary Entity**: Stores user-contributed restaurant data
- **Google Integration**: Optional `googlePlaceId` for API correlation
- **Location Data**: Coordinates, address, city, state
- **Business Info**: Hours, cuisine, price level, halal certification
- **Verification**: Manual verification system for quality control

#### User
- **Authentication Ready**: Prepared for user system integration
- **Preferences**: Cuisine preferences and location
- **Contributions**: Links to added restaurants and reviews

#### Review
- **Rating System**: 1-5 star ratings with optional titles
- **Rich Content**: Text reviews with visit dates
- **Moderation**: Approval system for content quality
- **Unique Constraint**: One review per user per restaurant

#### RestaurantPhoto
- **User Generated**: Only user-uploaded photos stored permanently
- **Metadata**: Captions, dimensions, primary photo designation
- **Google Photos**: Served via proxy API (not stored)

#### GooglePlacesCache
- **Temporary Storage**: 30-day cache for Google Places data
- **API Optimization**: Reduces API calls and improves performance
- **Compliance**: Respects Google Places API terms of service

### Indexing Strategy

```sql
-- Performance indexes
CREATE INDEX idx_restaurant_location ON Restaurant(latitude, longitude);
CREATE INDEX idx_restaurant_city ON Restaurant(city);
CREATE INDEX idx_restaurant_halal ON Restaurant(isHalal);
CREATE INDEX idx_restaurant_verified ON Restaurant(isVerified);
CREATE INDEX idx_restaurant_google_place ON Restaurant(googlePlaceId);

-- Review indexes
CREATE INDEX idx_review_restaurant ON Review(restaurantId);
CREATE INDEX idx_review_rating ON Review(rating);
CREATE INDEX idx_review_created ON Review(createdAt);

-- Cache management
CREATE INDEX idx_cache_expiry ON GooglePlacesCache(expiresAt);
```

## 🔌 API Integration

### Google Places API Integration

#### Search Flow
1. **Text Search**: `searchPlacesByText()` for restaurant discovery
2. **Halal Filtering**: `searchHalalRestaurants()` with halal-specific queries
3. **Place Details**: `getPlaceDetails()` for comprehensive information
4. **Photo Proxy**: `/api/photo/[photoReference]` for image serving

#### Rate Limiting & Caching
- **Request Optimization**: Batch requests where possible
- **Cache Strategy**: 30-day cache for place details
- **Error Handling**: Graceful degradation when API unavailable
- **Cost Management**: Minimal field requests to reduce costs

#### Compliance
- **Terms of Service**: Proper attribution and usage
- **Data Storage**: Only Place IDs stored permanently
- **Cache Expiry**: Automatic cleanup of expired data
- **User Consent**: Clear data usage disclosure

### Internal API Structure

#### Server Actions (`app/actions/restaurant-actions.js`)
- **searchRestaurants()**: Hybrid search (local + Google)
- **getRestaurantDetails()**: Unified detail fetching
- **addRestaurant()**: Local restaurant creation
- **addRestaurantFromGoogle()**: Google Places import

#### API Routes
- **Photo Proxy**: Secure Google Photos serving
- **Health Checks**: API connectivity testing
- **Future Extensions**: Ready for additional endpoints

## 🛣️ Routing Strategy

### App Router Implementation

#### Static Routes
- `/` - Home page with search interface
- `/add-restaurant` - Restaurant addition workflow

#### Dynamic Routes
- `/restaurant/[id]` - Local restaurant details
- `/restaurant/google/[placeId]` - Google Places restaurant details

#### Route Handlers
```javascript
// Local restaurant route
export default async function LocalRestaurantPage({ params }) {
  const { id } = await params;
  const result = await getRestaurantDetails(id, false);
  return <RestaurantDetailsPage restaurant={result.data} />;
}

// Google Places restaurant route  
export default async function GoogleRestaurantPage({ params }) {
  const { placeId } = await params;
  const result = await getRestaurantDetails(placeId, true);
  return <RestaurantDetailsPage restaurant={result.data} />;
}
```

#### SEO Optimization
- **Dynamic Metadata**: Restaurant-specific titles and descriptions
- **Structured Data**: Ready for schema.org markup
- **URL Structure**: SEO-friendly paths
- **Sitemap Generation**: Prepared for search engine indexing

## 🧩 Component Architecture

### Component Hierarchy

```
App Layout
├── Home Page
│   ├── SearchForm
│   └── RestaurantList
│       └── RestaurantCard (multiple)
├── Add Restaurant Page
│   └── GooglePlacesSearch
│       └── RestaurantSearchCard (multiple)
└── Restaurant Details Page
    ├── Photo Gallery
    ├── Restaurant Info
    ├── Contact Information
    ├── Reviews Section
    └── Action Buttons
```

### Component Design Principles

#### Separation of Concerns
- **Presentation Components**: Pure UI rendering
- **Container Components**: Data fetching and state management
- **Server Components**: Default for performance
- **Client Components**: Only when interactivity required

#### Reusability
- **Shared UI Components**: shadcn/ui component library
- **Custom Components**: Restaurant-specific reusable elements
- **Utility Functions**: Shared business logic
- **Type Safety**: Consistent prop interfaces

#### Performance
- **Server-Side Rendering**: Default rendering strategy
- **Image Optimization**: Next.js Image component
- **Code Splitting**: Automatic route-based splitting
- **Lazy Loading**: Components loaded on demand

### State Management

#### Server State
- **Database Queries**: Prisma ORM
- **API Calls**: Google Places integration
- **Caching**: Built-in Next.js caching
- **Revalidation**: Strategic cache invalidation

#### Client State
- **Form State**: React Hook Form (ready for implementation)
- **UI State**: React useState for local state
- **Search State**: URL-based state management
- **Photo Gallery**: Local component state

## 🔄 Data Flow

### Search Flow
```
User Input → SearchForm → Server Action → Database Query + Google API → RestaurantList → RestaurantCard
```

### Restaurant Addition Flow
```
User Search → GooglePlacesSearch → Google Places API → User Confirmation → Server Action → Database Insert → Redirect
```

### Restaurant Details Flow
```
URL Parameter → Route Handler → Server Action → Database Query + Google API → RestaurantDetailsPage
```

### Photo Serving Flow
```
Photo Reference → API Route → Google Photos API → Image Proxy → Client Display
```

## 🔒 Security Considerations

### API Security
- **Environment Variables**: Secure API key storage
- **Rate Limiting**: Prevent API abuse
- **Input Validation**: Server-side validation for all inputs
- **CORS Configuration**: Proper cross-origin settings

### Data Protection
- **SQL Injection**: Prisma ORM protection
- **XSS Prevention**: React's built-in protection
- **CSRF Protection**: Next.js built-in protection
- **Data Sanitization**: Input cleaning and validation

### Authentication (Future)
- **User Sessions**: Ready for authentication integration
- **Role-Based Access**: Admin/user permission structure
- **OAuth Integration**: Google/social login preparation
- **JWT Tokens**: Secure session management

## ⚡ Performance Optimizations

### Frontend Performance
- **Image Optimization**: Next.js Image component with lazy loading
- **Code Splitting**: Automatic route-based code splitting
- **Static Generation**: Pre-rendered pages where possible
- **Font Optimization**: Google Fonts optimization

### Backend Performance
- **Database Indexing**: Strategic index placement
- **Query Optimization**: Efficient Prisma queries
- **Caching Strategy**: Multi-layer caching approach
- **API Rate Limiting**: Efficient external API usage

### Caching Strategy
```
Browser Cache → CDN → Next.js Cache → Database Query Cache → Google Places Cache
```

## 🚀 Deployment Strategy

### Development Environment
- **Local Database**: PostgreSQL with Docker (recommended)
- **Environment Variables**: `.env.local` configuration
- **Hot Reloading**: Next.js development server
- **Database Seeding**: Automated test data generation

### Production Deployment
- **Platform**: Vercel (recommended) or self-hosted
- **Database**: Managed PostgreSQL (Vercel Postgres, Supabase, etc.)
- **Environment Variables**: Secure production configuration
- **Monitoring**: Error tracking and performance monitoring

### CI/CD Pipeline (Recommended)
```
Code Push → Automated Tests → Build Process → Database Migration → Deployment → Health Checks
```

### Scaling Considerations
- **Database Scaling**: Read replicas for heavy read workloads
- **CDN Integration**: Static asset optimization
- **Serverless Functions**: API route scaling
- **Monitoring**: Performance and error tracking

---

## 📚 Additional Resources

- [Next.js Documentation](https://nextjs.org/docs)
- [Prisma Documentation](https://www.prisma.io/docs)
- [Google Places API Documentation](https://developers.google.com/maps/documentation/places/web-service)
- [shadcn/ui Documentation](https://ui.shadcn.com)

---

*This architecture document is a living document and should be updated as the application evolves.*
